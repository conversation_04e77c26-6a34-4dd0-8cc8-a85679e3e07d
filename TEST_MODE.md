# SmartParent Test Mode

This document explains how to use the global test mode toggle for the SmartParent application.

## Overview

The test mode toggle allows you to switch between test and production environments across the entire system. When test mode is enabled:

- The server uses test database configuration
- The server uses test Stripe API keys
- The Chrome extension connects to the test server
- All other configurations use test values

When test mode is disabled, the system uses production configurations.

## Server Configuration

The server reads the test mode setting from the `TEST_MODE` environment variable. When `TEST_MODE` is set to `"true"`, the server runs in test mode. When `TEST_MODE` is set to `"false"` or not set, the server runs in production mode.

### Configuration Module

The configuration is managed by the `config.js` module in the `smartparent-k8s/cloud-function` directory. This module exports a configuration object with getters that return the appropriate values based on the test mode.

```javascript
// Example of how the configuration module works
const config = {
  isTestMode: process.env.TEST_MODE !== 'false',

  get serverUrl() {
    return this.isTestMode
      ? (process.env.TEST_SERVER_URL || 'https://test.qubitrhythm.com')
      : (process.env.SERVER_URL || 'https://smartparent.qubitrhythm.com');
  },

  // Other configuration getters...
};
```

## Toggling Test Mode

To toggle test mode for both the server and Chrome extension, use the unified `toggle-test-mode.sh` script.

### Prerequisites

- `kubectl` - Required for server configuration
- `jq` - Required for JSON parsing in the script
- `node` - Required for Chrome extension configuration

You can install these dependencies as follows:

```bash
# macOS
brew install kubectl jq node

# Ubuntu/Debian
apt-get install kubectl jq nodejs

# CentOS/RHEL
yum install kubectl jq nodejs
```

### Usage

```bash
# Enable test mode for both server and extension
./toggle-test-mode.sh --enable

# Disable test mode for both server and extension (use production)
./toggle-test-mode.sh --disable

# Toggle only the server
./toggle-test-mode.sh --enable --server-only
./toggle-test-mode.sh --disable --server-only

# Toggle only the Chrome extension
./toggle-test-mode.sh --enable --extension-only
./toggle-test-mode.sh --disable --extension-only
```

This script:
1. Updates the `TEST_MODE` environment variable in the Kubernetes deployments
2. Applies the appropriate secrets for the server
3. Updates the necessary files in the Chrome extension

After running the script, you need to reload the extension in Chrome for the changes to take effect. You can do this by going to `chrome://extensions` and clicking the refresh icon on the extension.

## Configuration Files

The system uses the following configuration files:

- `smartparent-k8s/k8s/cloud-function-secrets-test.yaml`: Test secrets for the server
- `smartparent-k8s/k8s/postgres-secrets-test.yaml`: Test secrets for the database
- `smartparent-k8s/k8s/cloud-function-secrets.yaml`: Production secrets for the server
- `smartparent-k8s/k8s/postgres-secrets.yaml`: Production secrets for the database

When test mode is enabled, the system uses the test secrets. When test mode is disabled, the system uses the production secrets.

## Deployment

When deploying to production, make sure to disable test mode:

```bash
# Disable test mode before deployment
./toggle-test-mode.sh --disable
```

This will ensure that both the server and Chrome extension use production configurations.

## Troubleshooting

If you encounter issues with test mode:

1. Check that the `TEST_MODE` environment variable is set correctly in the Kubernetes deployments:
   ```bash
   kubectl get deployment cloud-function -n smartparent -o yaml | grep TEST_MODE
   ```

2. Check that the correct secrets are applied:
   ```bash
   kubectl get secret cloud-function-secrets -n smartparent -o yaml
   kubectl get secret postgres-secrets -n smartparent -o yaml
   ```

3. Check the server logs for configuration information:
   ```bash
   kubectl logs deployment/cloud-function -n smartparent | grep "mode"
   ```

4. For the Chrome extension, check if the `.test-mode` file exists in the extension directory to verify the current mode.

## Notes

- The test mode toggle affects all aspects of the system, including database connections, Stripe API keys, and server URLs.
- When developing locally, it's recommended to keep test mode enabled to avoid accidental charges or data modifications in production.
- When deploying to production, always make sure test mode is disabled.
