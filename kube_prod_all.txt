$ kubectl get all -A 
NAMESPACE         NAME                                                            READY   STATUS    RESTARTS   AGE
cert-manager      pod/cert-manager-cainjector-f8c54c878-psgkw                     1/1     Running   0          3d1h
cert-manager      pod/cert-manager-df44f46c7-wlrcf                                1/1     Running   0          3d1h
cert-manager      pod/cert-manager-webhook-6445774848-f6llj                       1/1     Running   0          3d1h
gke-managed-cim   pod/kube-state-metrics-0                                        2/2     Running   0          3d1h
gmp-system        pod/collector-lw6kc                                             2/2     Running   0          3d1h
gmp-system        pod/collector-m8kmd                                             2/2     Running   0          3d1h
gmp-system        pod/gmp-operator-658769d5bd-cfms8                               1/1     Running   0          3d1h
ingress-nginx     pod/ingress-nginx-controller-7b967458dd-r2tf8                   1/1     Running   0          3d1h
kube-system       pod/event-exporter-gke-746c49d5b8-xk4px                         2/2     Running   0          3d1h
kube-system       pod/fluentbit-gke-6bdz4                                         3/3     Running   0          3d1h
kube-system       pod/fluentbit-gke-6l27w                                         3/3     Running   0          3d1h
kube-system       pod/gke-metadata-server-s8tdd                                   1/1     Running   0          3d1h
kube-system       pod/gke-metadata-server-x8r64                                   1/1     Running   0          3d1h
kube-system       pod/gke-metrics-agent-cn5wg                                     2/2     Running   0          3d1h
kube-system       pod/gke-metrics-agent-fm5ck                                     2/2     Running   0          3d1h
kube-system       pod/konnectivity-agent-5ffbfbf7dc-fh664                         2/2     Running   0          3d1h
kube-system       pod/konnectivity-agent-5ffbfbf7dc-j6xfd                         2/2     Running   0          3d1h
kube-system       pod/konnectivity-agent-autoscaler-5fc87b5445-dtb94              1/1     Running   0          3d1h
kube-system       pod/kube-dns-675cdcd645-kj8hs                                   4/4     Running   0          3d1h
kube-system       pod/kube-dns-675cdcd645-wb2qq                                   4/4     Running   0          3d1h
kube-system       pod/kube-dns-autoscaler-974d6495c-ln875                         1/1     Running   0          3d1h
kube-system       pod/kube-proxy-gke-smartparent-k8s-default-pool-3b888b6c-f5da   1/1     Running   0          3d1h
kube-system       pod/kube-proxy-gke-smartparent-k8s-default-pool-3b888b6c-v3zz   1/1     Running   0          3d1h
kube-system       pod/l7-default-backend-7f789c97fb-s2grl                         1/1     Running   0          3d1h
kube-system       pod/metrics-server-v1.32.2-844b977689-8glgt                     1/1     Running   0          3d1h
kube-system       pod/netd-8wbjs                                                  3/3     Running   0          3d1h
kube-system       pod/netd-d9rvv                                                  3/3     Running   0          3d1h
kube-system       pod/pdcsi-node-847v6                                            2/2     Running   0          3d1h
kube-system       pod/pdcsi-node-j5g77                                            2/2     Running   0          3d1h
smartparent       pod/cloud-function-c84658f84-4c4gd                              1/1     Running   0          3d1h
smartparent       pod/cloud-function-c84658f84-qr4kz                              1/1     Running   0          3d1h
smartparent       pod/cloud-function-tiptop-deployment-56b585bc8c-6jhbb           1/1     Running   0          3d1h
smartparent       pod/cloud-function-tiptop-deployment-56b585bc8c-xtmpv           1/1     Running   0          3d1h
smartparent       pod/postgres-6f4c688557-dx9f5                                   1/1     Running   0          3d1h

NAMESPACE       NAME                                         TYPE           CLUSTER-IP       EXTERNAL-IP     PORT(S)                      AGE
cert-manager    service/cert-manager                         ClusterIP      *************    <none>          9402/TCP                     116d
cert-manager    service/cert-manager-webhook                 ClusterIP      *************    <none>          443/TCP                      116d
default         service/kubernetes                           ClusterIP      ************     <none>          443/TCP                      118d
gmp-system      service/alertmanager                         ClusterIP      None             <none>          9093/TCP                     118d
gmp-system      service/gmp-operator                         ClusterIP      **************   <none>          8443/TCP,443/TCP             118d
gmp-system      service/rule-evaluator                       ClusterIP      **************   <none>          19092/TCP                    3d2h
ingress-nginx   service/ingress-nginx-controller             LoadBalancer   *************    *************   80:30230/TCP,443:30429/TCP   116d
ingress-nginx   service/ingress-nginx-controller-admission   ClusterIP      **************   <none>          443/TCP                      116d
kube-system     service/default-http-backend                 NodePort       *************    <none>          80:32179/TCP                 118d
kube-system     service/kube-dns                             ClusterIP      ************0    <none>          53/UDP,53/TCP                118d
kube-system     service/metrics-server                       ClusterIP      *************    <none>          443/TCP                      118d
smartparent     service/cloud-function                       LoadBalancer   *************    ************    80:30561/TCP,443:31458/TCP   118d
smartparent     service/cloud-function-tiptop-service        ClusterIP      *************    <none>          80/TCP,443/TCP               40d
smartparent     service/postgres                             ClusterIP      **************   <none>          5432/TCP                     118d

NAMESPACE     NAME                                                    DESIRED   CURRENT   READY   UP-TO-DATE   AVAILABLE   NODE SELECTOR                                                        AGE
gmp-system    daemonset.apps/collector                                2         2         2       2            2           <none>                                                               118d
kube-system   daemonset.apps/fluentbit-gke                            2         2         2       2            2           kubernetes.io/os=linux                                               118d
kube-system   daemonset.apps/fluentbit-gke-256pd                      0         0         0       0            0           kubernetes.io/os=linux                                               118d
kube-system   daemonset.apps/fluentbit-gke-max                        0         0         0       0            0           kubernetes.io/os=linux                                               118d
kube-system   daemonset.apps/gke-metadata-server                      2         2         2       2            2           iam.gke.io/gke-metadata-server-enabled=true,kubernetes.io/os=linux   118d
kube-system   daemonset.apps/gke-metrics-agent                        2         2         2       2            2           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-10             0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-100            0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-20             0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-200            0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-50             0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-scaling-500            0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/gke-metrics-agent-windows                0         0         0       0            0           kubernetes.io/os=windows                                             118d
kube-system   daemonset.apps/kube-proxy                               0         0         0       0            0           kubernetes.io/os=linux,node.kubernetes.io/kube-proxy-ds-ready=true   118d
kube-system   daemonset.apps/maintenance-handler                      0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/metadata-proxy-v0.1                      0         0         0       0            0           cloud.google.com/metadata-proxy-ready=true,kubernetes.io/os=linux    118d
kube-system   daemonset.apps/nccl-fastsocket-installer                0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/netd                                     2         2         2       2            2           cloud.google.com/gke-netd-ready=true,kubernetes.io/os=linux          118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-large-cos       0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-large-ubuntu    0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-medium-cos      0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-medium-ubuntu   0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-small-cos       0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/nvidia-gpu-device-plugin-small-ubuntu    0         0         0       0            0           <none>                                                               118d
kube-system   daemonset.apps/pdcsi-node                               2         2         2       2            2           kubernetes.io/os=linux                                               118d
kube-system   daemonset.apps/pdcsi-node-windows                       0         0         0       0            0           kubernetes.io/os=windows                                             118d
kube-system   daemonset.apps/runsc-metric-server                      0         0         0       0            0           kubernetes.io/os=linux,sandbox.gke.io/runtime=gvisor                 118d
kube-system   daemonset.apps/tpu-device-plugin                        0         0         0       0            0           <none>                                                               118d

NAMESPACE       NAME                                               READY   UP-TO-DATE   AVAILABLE   AGE
cert-manager    deployment.apps/cert-manager                       1/1     1            1           116d
cert-manager    deployment.apps/cert-manager-cainjector            1/1     1            1           116d
cert-manager    deployment.apps/cert-manager-webhook               1/1     1            1           116d
gmp-system      deployment.apps/gmp-operator                       1/1     1            1           118d
gmp-system      deployment.apps/rule-evaluator                     0/0     0            0           118d
ingress-nginx   deployment.apps/ingress-nginx-controller           1/1     1            1           116d
kube-system     deployment.apps/event-exporter-gke                 1/1     1            1           118d
kube-system     deployment.apps/konnectivity-agent                 2/2     2            2           118d
kube-system     deployment.apps/konnectivity-agent-autoscaler      1/1     1            1           118d
kube-system     deployment.apps/kube-dns                           2/2     2            2           118d
kube-system     deployment.apps/kube-dns-autoscaler                1/1     1            1           118d
kube-system     deployment.apps/l7-default-backend                 1/1     1            1           118d
kube-system     deployment.apps/metrics-server-v1.32.2             1/1     1            1           3d2h
smartparent     deployment.apps/cloud-function                     2/2     2            2           117d
smartparent     deployment.apps/cloud-function-tiptop-deployment   2/2     2            2           40d
smartparent     deployment.apps/postgres                           1/1     1            1           118d

NAMESPACE       NAME                                                          DESIRED   CURRENT   READY   AGE
cert-manager    replicaset.apps/cert-manager-cainjector-f8c54c878             1         1         1       116d
cert-manager    replicaset.apps/cert-manager-df44f46c7                        1         1         1       116d
cert-manager    replicaset.apps/cert-manager-webhook-6445774848               1         1         1       116d
gmp-system      replicaset.apps/gmp-operator-55d8c7c79c                       0         0         0       80d
gmp-system      replicaset.apps/gmp-operator-5f79666f9                        0         0         0       101d
gmp-system      replicaset.apps/gmp-operator-658769d5bd                       1         1         1       3d2h
gmp-system      replicaset.apps/gmp-operator-676b6c55b4                       0         0         0       69d
gmp-system      replicaset.apps/gmp-operator-69b799b75b                       0         0         0       94d
gmp-system      replicaset.apps/gmp-operator-6bc76c6899                       0         0         0       30d
gmp-system      replicaset.apps/gmp-operator-747c59c4fb                       0         0         0       9d
gmp-system      replicaset.apps/gmp-operator-786f58bf48                       0         0         0       108d
gmp-system      replicaset.apps/gmp-operator-7b86b6df9f                       0         0         0       115d
gmp-system      replicaset.apps/gmp-operator-8fc885b46                        0         0         0       87d
gmp-system      replicaset.apps/gmp-operator-fcbb46857                        0         0         0       67d
gmp-system      replicaset.apps/rule-evaluator-55b99f777d                     0         0         0       30d
gmp-system      replicaset.apps/rule-evaluator-5cb59cdd84                     0         0         0       67d
gmp-system      replicaset.apps/rule-evaluator-5d488fdc9                      0         0         0       3d2h
gmp-system      replicaset.apps/rule-evaluator-66fd94c8d4                     0         0         0       118d
gmp-system      replicaset.apps/rule-evaluator-6cdb6dccbd                     0         0         0       9d
gmp-system      replicaset.apps/rule-evaluator-6f659bc47f                     0         0         0       118d
ingress-nginx   replicaset.apps/ingress-nginx-controller-7b967458dd           1         1         1       116d
kube-system     replicaset.apps/event-exporter-gke-5c5b457d58                 0         0         0       67d
kube-system     replicaset.apps/event-exporter-gke-647f8c488                  0         0         0       118d
kube-system     replicaset.apps/event-exporter-gke-688f74cb76                 0         0         0       87d
kube-system     replicaset.apps/event-exporter-gke-68b5677bff                 0         0         0       101d
kube-system     replicaset.apps/event-exporter-gke-69d9b8fddc                 0         0         0       30d
kube-system     replicaset.apps/event-exporter-gke-6cc4688488                 0         0         0       115d
kube-system     replicaset.apps/event-exporter-gke-746c49d5b8                 1         1         1       3d2h
kube-system     replicaset.apps/event-exporter-gke-845f48cff6                 0         0         0       69d
kube-system     replicaset.apps/konnectivity-agent-5ffbfbf7dc                 2         2         2       9d
kube-system     replicaset.apps/konnectivity-agent-6dfcc59                    0         0         0       94d
kube-system     replicaset.apps/konnectivity-agent-778798fc56                 0         0         0       80d
kube-system     replicaset.apps/konnectivity-agent-869d6d68c9                 0         0         0       67d
kube-system     replicaset.apps/konnectivity-agent-954c787cd                  0         0         0       118d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-57688df5df      0         0         0       69d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-5fc87b5445      1         1         1       9d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-64b6d55f74      0         0         0       94d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-7d68996447      0         0         0       80d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-bcf47ffcb       0         0         0       118d
kube-system     replicaset.apps/konnectivity-agent-autoscaler-cc5bd5684       0         0         0       67d
kube-system     replicaset.apps/konnectivity-agent-d5c57dc4f                  0         0         0       69d
kube-system     replicaset.apps/kube-dns-56c7bb75c8                           0         0         0       108d
kube-system     replicaset.apps/kube-dns-57dc6cd89b                           0         0         0       115d
kube-system     replicaset.apps/kube-dns-5868b698b                            0         0         0       9d
kube-system     replicaset.apps/kube-dns-5b5986f8c8                           0         0         0       30d
kube-system     replicaset.apps/kube-dns-5fff9ff5d5                           0         0         0       118d
kube-system     replicaset.apps/kube-dns-675cdcd645                           2         2         2       3d2h
kube-system     replicaset.apps/kube-dns-75674478d7                           0         0         0       87d
kube-system     replicaset.apps/kube-dns-7cd67ff655                           0         0         0       80d
kube-system     replicaset.apps/kube-dns-7f6b5b8666                           0         0         0       67d
kube-system     replicaset.apps/kube-dns-autoscaler-54dd58df76                0         0         0       94d
kube-system     replicaset.apps/kube-dns-autoscaler-5bf856b97c                0         0         0       30d
kube-system     replicaset.apps/kube-dns-autoscaler-647f7d44f4                0         0         0       87d
kube-system     replicaset.apps/kube-dns-autoscaler-6f896b6968                0         0         0       118d
kube-system     replicaset.apps/kube-dns-autoscaler-757fcd4dd6                0         0         0       9d
kube-system     replicaset.apps/kube-dns-autoscaler-8b7698c76                 0         0         0       67d
kube-system     replicaset.apps/kube-dns-autoscaler-974d6495c                 1         1         1       3d2h
kube-system     replicaset.apps/kube-dns-autoscaler-dfd87c46b                 0         0         0       80d
kube-system     replicaset.apps/kube-dns-fb9cfb447                            0         0         0       94d
kube-system     replicaset.apps/l7-default-backend-5c8c8fcb96                 0         0         0       9d
kube-system     replicaset.apps/l7-default-backend-7bd9cb456f                 0         0         0       118d
kube-system     replicaset.apps/l7-default-backend-7f789c97fb                 1         1         1       3d2h
kube-system     replicaset.apps/l7-default-backend-87b58b54c                  0         0         0       67d
kube-system     replicaset.apps/l7-default-backend-ff55c95c9                  0         0         0       94d
kube-system     replicaset.apps/metrics-server-v1.32.2-668ffbc689             0         0         0       3d2h
kube-system     replicaset.apps/metrics-server-v1.32.2-844b977689             1         1         1       3d2h
smartparent     replicaset.apps/cloud-function-5d89df6f46                     0         0         0       63d
smartparent     replicaset.apps/cloud-function-5dcc4c44f6                     0         0         0       63d
smartparent     replicaset.apps/cloud-function-5f7788bcd9                     0         0         0       63d
smartparent     replicaset.apps/cloud-function-6c684884fd                     0         0         0       62d
smartparent     replicaset.apps/cloud-function-6fd6479d68                     0         0         0       48d
smartparent     replicaset.apps/cloud-function-7777b68d7d                     0         0         0       63d
smartparent     replicaset.apps/cloud-function-77cc6c85dc                     0         0         0       47d
smartparent     replicaset.apps/cloud-function-77d45db488                     0         0         0       63d
smartparent     replicaset.apps/cloud-function-85cff9998                      0         0         0       47d
smartparent     replicaset.apps/cloud-function-c84658f84                      2         2         2       47d
smartparent     replicaset.apps/cloud-function-cb476d6d5                      0         0         0       62d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-56b585bc8c   2         2         2       28d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-688584c6dc   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-6c9598d699   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-6fc4bdc6c9   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-6fd4df7587   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-757844c948   0         0         0       28d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-7859c9795d   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-7876787cdb   0         0         0       28d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-7dc6d89fbd   0         0         0       29d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-855f89595b   0         0         0       28d
smartparent     replicaset.apps/cloud-function-tiptop-deployment-85df9db9d9   0         0         0       29d
smartparent     replicaset.apps/postgres-6f4c688557                           1         1         1       118d

NAMESPACE         NAME                                  READY   AGE
gke-managed-cim   statefulset.apps/kube-state-metrics   1/1     118d
gmp-system        statefulset.apps/alertmanager         0/0     118d

NAMESPACE         NAME                                                     REFERENCE                        TARGETS                  MINPODS   MAXPODS   REPLICAS   AGE
gke-managed-cim   horizontalpodautoscaler.autoscaling/kube-state-metrics   StatefulSet/kube-state-metrics   memory: 26308608/400Mi   1         10        1          118d

NAMESPACE       NAME                                       STATUS     COMPLETIONS   DURATION   AGE
ingress-nginx   job.batch/ingress-nginx-admission-create   Complete   1/1           6s         116d
ingress-nginx   job.batch/ingress-nginx-admission-patch    Complete   1/1           8s         116d
smartparent     job.batch/db-init                          Complete   1/1           18s        118d
