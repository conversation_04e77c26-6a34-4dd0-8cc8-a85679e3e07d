#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in deploy script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details
PROJECT_ID="smartparent"
CLUSTER_NAME="smartparent-k8s"
ZONE="us-central1-f"
REGION="us-central1"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Delete existing cluster if it exists
echo "Checking for existing cluster..."
if gcloud container clusters list --filter="name=$CLUSTER_NAME" --format="get(name)" | grep -q "^$CLUSTER_NAME$"; then
    echo "Deleting existing cluster..."
    gcloud container clusters delete $CLUSTER_NAME --zone $ZONE --quiet
fi

# Create new cluster with proper configuration
echo "Creating new GKE cluster..."
gcloud container clusters create $CLUSTER_NAME \
    --zone $ZONE \
    --num-nodes 2 \
    --machine-type e2-standard-2 \
    --disk-size 50 \
    --enable-ip-alias \
    --release-channel regular \
    --workload-pool=$PROJECT_ID.svc.id.goog

# Get cluster credentials
echo "Getting cluster credentials..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE

# Create namespace if it doesn't exist
kubectl create namespace smartparent --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations with proper order and verification
echo "Applying Kubernetes configurations..."

echo "Creating secrets..."
kubectl apply -f k8s/postgres-secrets.yaml -n smartparent

echo "Creating storage..."
kubectl apply -f k8s/postgres-pvc.yaml -n smartparent
# Wait for PVC to be bound
echo "Waiting for PVC to be bound..."
###kubectl wait --for=condition=bound pvc/postgres-pvc -n smartparent --timeout=180s
if ! kubectl wait --for=condition=bound pvc/postgres-pvc -n smartparent --timeout=300s; then
    echo "PVC binding failed - checking status:"
    kubectl describe pvc/postgres-pvc -n smartparent
    exit 1
fi

echo "Deploying PostgreSQL..."
kubectl apply -f k8s/postgres-deployment.yaml -n smartparent
kubectl apply -f k8s/postgres-service.yaml -n smartparent

# Wait for PostgreSQL pod to be ready
echo "Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres -n smartparent --timeout=180s

echo "Initializing database..."
kubectl apply -f k8s/db-init-configmap.yaml -n smartparent
kubectl apply -f k8s/db-init-job.yaml -n smartparent

# Wait for database initialization to complete
echo "Waiting for database initialization..."
kubectl wait --for=condition=complete job/db-init -n smartparent --timeout=300s

echo "PostgreSQL deployment completed successfully!"

# Build and push cloud function image
echo "Building cloud function Docker image..."
docker build -t gcr.io/$PROJECT_ID/cloud-function:latest -f cloud-function/Dockerfile cloud-function

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing cloud function image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/cloud-function:latest

echo "Deploying cloud function..."
kubectl apply -f k8s/cloud-function-secrets.yaml -n smartparent
kubectl apply -f k8s/cloud-function-deployment.yaml -n smartparent
kubectl apply -f k8s/cloud-function-service.yaml -n smartparent

# Wait for cloud function deployment to be ready
echo "Waiting for cloud function to be ready..."
kubectl wait --for=condition=available deployment/cloud-function -n smartparent --timeout=180s

# Verify all deployments
echo "Verifying deployments..."
kubectl get pods -n smartparent
kubectl get services -n smartparent
kubectl get deployments -n smartparent

echo "All deployments completed successfully!"
echo "Cloud function will be available at the LoadBalancer IP address shown above."
