# Use an official Node.js runtime as a parent image
FROM node:18-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and install dependencies
# Use trial-monitor-package.json specifically
COPY trial-monitor-package.json ./package.json
RUN npm install --omit=dev

# Copy the application code
COPY trial-monitor.js ./

# Make port 8080 available to the world outside this container (optional, good practice if it were a server)
# EXPOSE 8080

# Define the command to run your app using CMD which defines your runtime
CMD [ "node", "trial-monitor.js" ]
