CREATE TABLE IF NOT EXISTS installations (
    ip_hash TEXT PRIMARY KEY,
    email TEXT NULL, -- Email associated with this installation/user
    status TEXT NOT NULL, -- e.g., Free, TrialPendingEmail, TrialActive, TrialExpired, Subscribed
    install_timestamp TIMESTAMP DEFAULT NOW(),
    activation_timestamp TIMESTAMP NULL, -- Record when user successfully activates with email
    trial_used BOOLEAN DEFAULT FALSE, -- Record if the Premium trial has been used
    trial_reminder_sent_at TIMESTAMP NULL -- Record when the expiry reminder email was sent
);

CREATE INDEX IF NOT EXISTS idx_installations_ip_hash ON installations(ip_hash);
CREATE INDEX IF NOT EXISTS idx_installations_email ON installations(email); -- Index email for lookups
CREATE INDEX IF NOT EXISTS idx_installations_status ON installations(status);

-- New table for storing uninstall survey feedback
CREATE TABLE IF NOT EXISTS survey_responses (
    response_id SERIAL PRIMARY KEY,
    email TEXT NULL, -- User's email if provided/available
    ip_hash TEXT NULL, -- Fallback identifier if email not available
    reason TEXT NOT NULL, -- Selected reason (e.g., 'price', 'complexity')
    details TEXT NULL, -- Optional text details
    calculated_usage_duration_days INT NULL, -- Duration from activation to survey submission
    submission_timestamp TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_survey_responses_email ON survey_responses(email);
CREATE INDEX IF NOT EXISTS idx_survey_responses_ip_hash ON survey_responses(ip_hash);
