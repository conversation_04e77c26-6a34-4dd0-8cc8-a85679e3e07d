apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cloud-function-ingress
  namespace: smartparent
  annotations:
    cert-manager.io/cluster-issuer: selfsigned-issuer # Changed from letsencrypt-prod for local testing
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # cert-manager.io/acme-challenge-type: http01 # Removed for self-signed issuer
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-origin: "chrome-extension://*, https://smartparent.qubitrhythm.com"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - smartparent.qubitrhythm.com
    - extension.smartparent.qubitrhythm.com
    secretName: smartparent-tls
  rules:
  - host: smartparent.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function
            port:
              number: 80
  - host: extension.smartparent.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function
            port:
              number: 80
