#!/bin/bash

# check_stripe_users.sh
# Lists all customers and their subscription status from Stripe

# Get Stripe secret key from environment or prompt user
if [ -z "$STRIPE_SECRET_KEY" ]; then
    read -p "Enter your Stripe secret key: " STRIPE_SECRET_KEY
fi

# Function to format Unix timestamp to readable date
format_date() {
    date -r "$1" "+%Y-%m-%d %H:%M:%S"
}

# Function to get customer details including subscriptions
get_customer_details() {
    echo "Fetching Stripe customers..."
    echo "----------------------------------------"
    
    # Get list of customers with expanded subscription data
    customers=$(curl -s -X GET \
        -H "Authorization: Bearer $STRIPE_SECRET_KEY" \
        "https://api.stripe.com/v1/customers?limit=100&expand[]=data.subscriptions")
    
    echo "$customers" | jq -r '.data[] | {
        email: .email,
        id: .id,
        created: .created,
        subscriptions: .subscriptions.data[] | {
            status: .status,
            plan: .items.data[0].price.nickname,
            current_period_end: .current_period_end
        }
    }' | while read -r line; do
        if [[ $line == "{" ]]; then
            continue
        elif [[ $line == "}" ]]; then
            echo "----------------------------------------"
        else
            # Format and display customer information
            key=$(echo "$line" | cut -d: -f1 | tr -d ' "')
            value=$(echo "$line" | cut -d: -f2- | tr -d ' "')
            
            case "$key" in
                "email")
                    echo "Email: $value"
                    ;;
                "id")
                    echo "Customer ID: $value"
                    ;;
                "created")
                    echo "Created: $(format_date "$value")"
                    ;;
                "status")
                    echo "Subscription Status: $value"
                    ;;
                "plan")
                    echo "Plan: $value"
                    ;;
                "current_period_end")
                    echo "Current Period Ends: $(format_date "$value")"
                    ;;
            esac
        fi
    done
}

# Main execution
get_customer_details
