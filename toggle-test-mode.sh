#!/bin/bash
# Unified script to toggle test mode for both server and Chrome extension

set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
NAMESPACE="smartparent"
CLOUD_FUNCTION_DEPLOYMENT="cloud-function"
TRIAL_MONITOR_CRONJOB="smartparent-trial-monitor"
EXTENSION_DIR="smartparent-extension-prod"
SERVER_DIR="smartparent-k8s-prod"

# --- Functions ---
handle_error() {
    echo "Error occurred in script at line $1."
    exit 1
}

trap 'handle_error $LINENO' ERR

print_header() {
    echo ""
    echo "===== $1 ====="
    echo ""
}

# --- Parse arguments ---
ENABLE_TEST_MODE=false
SHOW_HELP=false
SERVER_ONLY=false
EXTENSION_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --enable)
            ENABLE_TEST_MODE=true
            shift
            ;;
        --disable)
            ENABLE_TEST_MODE=false
            shift
            ;;
        --server-only)
            SERVER_ONLY=true
            shift
            ;;
        --extension-only)
            EXTENSION_ONLY=true
            shift
            ;;
        --help|-h)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            SHOW_HELP=true
            shift
            ;;
    esac
done

if [ "$SHOW_HELP" = true ]; then
    echo "Usage: $0 [--enable|--disable] [--server-only|--extension-only]"
    echo ""
    echo "Options:"
    echo "  --enable           Enable test mode"
    echo "  --disable          Disable test mode (default)"
    echo "  --server-only      Only toggle server test mode"
    echo "  --extension-only   Only toggle Chrome extension test mode"
    echo "  --help, -h         Show this help message"
    exit 0
fi

# --- Set test mode value ---
TEST_MODE_VALUE="false"
if [ "$ENABLE_TEST_MODE" = true ]; then
    TEST_MODE_VALUE="true"
    echo "Enabling test mode..."
else
    echo "Disabling test mode (using production mode)..."
fi

# --- Toggle server test mode ---
toggle_server_test_mode() {
    print_header "SERVER CONFIGURATION"

    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo "kubectl is not installed or not in PATH. Skipping server configuration."
        return
    fi

    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        echo "jq is not installed or not in PATH. This is required for JSON parsing."
        echo "Please install jq and try again."
        echo "On macOS: brew install jq"
        echo "On Ubuntu/Debian: apt-get install jq"
        echo "On CentOS/RHEL: yum install jq"
        return
    fi

    # Check if namespace exists
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        echo "Namespace $NAMESPACE does not exist. Skipping server configuration."
        return
    fi

    # Update cloud-function deployment
    if kubectl get deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE &> /dev/null; then
        echo "Updating TEST_MODE in $CLOUD_FUNCTION_DEPLOYMENT deployment..."
        echo "  Manual equivalent: kubectl patch deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --type=json -p='[{\"op\": \"replace\", \"path\": \"/spec/template/spec/containers/0/env/[INDEX]/value\", \"value\": \"$TEST_MODE_VALUE\"}]'"

        # First, find the index of the TEST_MODE environment variable
        ENV_INDEX=$(kubectl get deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE -o json | \
            jq '.spec.template.spec.containers[0].env | map(.name == "TEST_MODE") | index(true)')

        if [ "$ENV_INDEX" != "null" ] && [ ! -z "$ENV_INDEX" ]; then
            echo "  Found TEST_MODE at index $ENV_INDEX, updating value to: $TEST_MODE_VALUE"
            echo "  Executing: kubectl patch deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --type=json -p='[{\"op\": \"replace\", \"path\": \"/spec/template/spec/containers/0/env/$ENV_INDEX/value\", \"value\": \"$TEST_MODE_VALUE\"}]'"

            # Use patch to update the TEST_MODE environment variable
            kubectl patch deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --type=json \
                -p="[{\"op\": \"replace\", \"path\": \"/spec/template/spec/containers/0/env/$ENV_INDEX/value\", \"value\": \"$TEST_MODE_VALUE\"}]"

            echo "  ✓ TEST_MODE environment variable updated to: $TEST_MODE_VALUE"
        else
            echo "  TEST_MODE environment variable not found in deployment. Adding it..."
            echo "  Executing: kubectl patch deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --type=json -p='[{\"op\": \"add\", \"path\": \"/spec/template/spec/containers/0/env/-\", \"value\": {\"name\": \"TEST_MODE\", \"value\": \"$TEST_MODE_VALUE\"}}]'"

            # Add the TEST_MODE environment variable
            kubectl patch deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --type=json \
                -p="[{\"op\": \"add\", \"path\": \"/spec/template/spec/containers/0/env/-\", \"value\": {\"name\": \"TEST_MODE\", \"value\": \"$TEST_MODE_VALUE\"}}]"

            echo "  ✓ TEST_MODE environment variable added with value: $TEST_MODE_VALUE"
        fi

        echo "  Restarting deployment to apply changes..."
        echo "  Manual equivalent: kubectl rollout restart deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE"
        kubectl rollout restart deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE

        echo "  Waiting for deployment to be ready..."
        echo "  Manual equivalent: kubectl rollout status deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --timeout=60s"
        kubectl rollout status deployment $CLOUD_FUNCTION_DEPLOYMENT -n $NAMESPACE --timeout=60s
        echo "  ✓ Deployment restart completed"
    else
        echo "  ⚠ Deployment $CLOUD_FUNCTION_DEPLOYMENT not found in namespace $NAMESPACE. Skipping."
    fi

    # Update trial-monitor cronjob
    if kubectl get cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE &> /dev/null; then
        echo "Updating TEST_MODE in $TRIAL_MONITOR_CRONJOB cronjob..."
        echo "  Manual equivalent: kubectl patch cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE --type=json -p='[{\"op\": \"replace\", \"path\": \"/spec/jobTemplate/spec/template/spec/containers/0/env/[INDEX]/value\", \"value\": \"$TEST_MODE_VALUE\"}]'"

        # First, find the index of the TEST_MODE environment variable
        ENV_INDEX=$(kubectl get cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE -o json | \
            jq '.spec.jobTemplate.spec.template.spec.containers[0].env | map(.name == "TEST_MODE") | index(true)')

        if [ "$ENV_INDEX" != "null" ] && [ ! -z "$ENV_INDEX" ]; then
            echo "  Found TEST_MODE at index $ENV_INDEX, updating value to: $TEST_MODE_VALUE"
            echo "  Executing: kubectl patch cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE --type=json -p='[{\"op\": \"replace\", \"path\": \"/spec/jobTemplate/spec/template/spec/containers/0/env/$ENV_INDEX/value\", \"value\": \"$TEST_MODE_VALUE\"}]'"

            # Use patch to update the TEST_MODE environment variable
            kubectl patch cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE --type=json \
                -p="[{\"op\": \"replace\", \"path\": \"/spec/jobTemplate/spec/template/spec/containers/0/env/$ENV_INDEX/value\", \"value\": \"$TEST_MODE_VALUE\"}]"

            echo "  ✓ TEST_MODE environment variable updated to: $TEST_MODE_VALUE"
        else
            echo "  TEST_MODE environment variable not found in cronjob. Adding it..."
            echo "  Executing: kubectl patch cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE --type=json -p='[{\"op\": \"add\", \"path\": \"/spec/jobTemplate/spec/template/spec/containers/0/env/-\", \"value\": {\"name\": \"TEST_MODE\", \"value\": \"$TEST_MODE_VALUE\"}}]'"

            # Add the TEST_MODE environment variable
            kubectl patch cronjob $TRIAL_MONITOR_CRONJOB -n $NAMESPACE --type=json \
                -p="[{\"op\": \"add\", \"path\": \"/spec/jobTemplate/spec/template/spec/containers/0/env/-\", \"value\": {\"name\": \"TEST_MODE\", \"value\": \"$TEST_MODE_VALUE\"}}]"

            echo "  ✓ TEST_MODE environment variable added with value: $TEST_MODE_VALUE"
        fi

        echo "  ✓ CronJob $TRIAL_MONITOR_CRONJOB updated successfully"
    else
        echo "  ⚠ CronJob $TRIAL_MONITOR_CRONJOB not found in namespace $NAMESPACE. Skipping."
    fi

    # Update secrets if needed
    if [ "$ENABLE_TEST_MODE" = true ]; then
        echo "Switching to test secrets..."
        echo "  This will update Stripe keys, price IDs, and server URLs to use test environment"

        # Check if cloud-function-secrets-test.yaml exists
        if [ -f "$SERVER_DIR/k8s/cloud-function-secrets-test.yaml" ]; then
            echo "  Applying test cloud-function secrets..."
            echo "  Manual equivalent: kubectl apply -f $SERVER_DIR/k8s/cloud-function-secrets-test.yaml"
            echo "  Changes:"
            echo "    - STRIPE_SECRET_KEY: sk_test_* (test key)"
            echo "    - PLAN_PRICE_IDS_MONTHLY: price_1ROXMaAR7VlUIrExs1QtF2ma (test monthly $2.99)"
            echo "    - PLAN_PRICE_IDS_ANNUAL: price_1ROXIUAR7VlUIrExhmftEcT2 (test annual $29.99)"
            echo "    - SERVER_URL: https://test.qubitrhythm.com"
            kubectl apply -f $SERVER_DIR/k8s/cloud-function-secrets-test.yaml
            echo "  ✓ Test cloud-function secrets applied"
        else
            echo "  ⚠ Warning: $SERVER_DIR/k8s/cloud-function-secrets-test.yaml not found. Skipping test secrets."
        fi

        # Check if postgres-secrets-test.yaml exists
        if [ -f "$SERVER_DIR/k8s/postgres-secrets-test.yaml" ]; then
            echo "  Applying test postgres secrets..."
            echo "  Manual equivalent: kubectl apply -f $SERVER_DIR/k8s/postgres-secrets-test.yaml"
            echo "  Changes:"
            echo "    - POSTGRES_DB: smartparent (same as production for compatibility)"
            echo "    - POSTGRES_PASSWORD: Happy4SP$ (same as production for compatibility)"
            kubectl apply -f $SERVER_DIR/k8s/postgres-secrets-test.yaml
            echo "  ✓ Test postgres secrets applied"
        else
            echo "  ⚠ Warning: $SERVER_DIR/k8s/postgres-secrets-test.yaml not found. Skipping test secrets."
        fi
    else
        echo "Switching to production secrets..."
        echo "  This will update Stripe keys, price IDs, and server URLs to use production environment"

        # Check if cloud-function-secrets.yaml exists
        if [ -f "$SERVER_DIR/k8s/cloud-function-secrets.yaml" ]; then
            echo "  Applying production cloud-function secrets..."
            echo "  Manual equivalent: kubectl apply -f $SERVER_DIR/k8s/cloud-function-secrets.yaml"
            echo "  Changes:"
            echo "    - STRIPE_SECRET_KEY: sk_live_* (live key)"
            echo "    - PLAN_PRICE_IDS_MONTHLY: price_1RP98oAR7VlUIrExatqrxAEE (production monthly $2.99)"
            echo "    - PLAN_PRICE_IDS_ANNUAL: price_1RP99PAR7VlUIrExQiPIcgZl (production annual $29.99)"
            echo "    - SERVER_URL: https://smartparent.qubitrhythm.com"
            kubectl apply -f $SERVER_DIR/k8s/cloud-function-secrets.yaml
            echo "  ✓ Production cloud-function secrets applied"
        else
            echo "  ⚠ Warning: $SERVER_DIR/k8s/cloud-function-secrets.yaml not found. Skipping production secrets."
        fi

        # Check if postgres-secrets.yaml exists
        if [ -f "$SERVER_DIR/k8s/postgres-secrets.yaml" ]; then
            echo "  Applying production postgres secrets..."
            echo "  Manual equivalent: kubectl apply -f $SERVER_DIR/k8s/postgres-secrets.yaml"
            echo "  Changes:"
            echo "    - POSTGRES_DB: smartparent"
            echo "    - POSTGRES_PASSWORD: Happy4SP$"
            kubectl apply -f $SERVER_DIR/k8s/postgres-secrets.yaml
            echo "  ✓ Production postgres secrets applied"
        else
            echo "  ⚠ Warning: $SERVER_DIR/k8s/postgres-secrets.yaml not found. Skipping production secrets."
        fi
    fi

    echo "Server test mode has been $([ "$ENABLE_TEST_MODE" = true ] && echo "ENABLED" || echo "DISABLED")."
}

# --- Toggle Chrome extension test mode ---
toggle_extension_test_mode() {
    print_header "CHROME EXTENSION CONFIGURATION"

    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        echo "Node.js is not installed or not in PATH. Skipping Chrome extension configuration."
        return
    fi

    # Check if the extension directory exists
    if [ ! -d "$EXTENSION_DIR" ]; then
        echo "Chrome extension directory $EXTENSION_DIR not found. Skipping Chrome extension configuration."
        return
    fi

    # Run the Node.js script to toggle extension test mode
    echo "Running Chrome extension test mode toggle script..."
    echo "  This will update extension files to point to the correct server environment"

    if [ "$ENABLE_TEST_MODE" = true ]; then
        echo "  Enabling test mode for Chrome extension..."
        echo "  Manual equivalent: node $EXTENSION_DIR/toggle-test-mode.js --enable"
        echo "  Changes that will be made:"
        echo "    - background.js: SERVER_URL → https://test.qubitrhythm.com"
        echo "    - background.js: uninstallUrl → https://test.qubitrhythm.com/staticHosting/uninstall_survey.html"
        echo "    - popup/popup.js: SERVER_URL → https://test.qubitrhythm.com"
        echo "    - subscribe.js: isTestMode → true"
        echo "    - subscribe.js: SERVER_URL → https://test.qubitrhythm.com"
        echo "    - Creates .test-mode indicator file"
        echo ""
        node $EXTENSION_DIR/toggle-test-mode.js --enable
        echo "  ✓ Extension configured for test environment"
    else
        echo "  Disabling test mode for Chrome extension (production mode)..."
        echo "  Manual equivalent: node $EXTENSION_DIR/toggle-test-mode.js --disable"
        echo "  Changes that will be made:"
        echo "    - background.js: SERVER_URL → https://smartparent.qubitrhythm.com"
        echo "    - background.js: uninstallUrl → https://smartparent.qubitrhythm.com/staticHosting/uninstall_survey.html"
        echo "    - popup/popup.js: SERVER_URL → https://smartparent.qubitrhythm.com"
        echo "    - subscribe.js: isTestMode → false"
        echo "    - subscribe.js: SERVER_URL → https://smartparent.qubitrhythm.com"
        echo "    - Removes .test-mode indicator file"
        echo ""
        node $EXTENSION_DIR/toggle-test-mode.js --disable
        echo "  ✓ Extension configured for production environment"
    fi

    echo "Chrome extension test mode has been $([ "$ENABLE_TEST_MODE" = true ] && echo "ENABLED" || echo "DISABLED")."
    echo "Note: You need to reload the extension in Chrome for changes to take effect."
}

# --- Main ---
if [ "$SERVER_ONLY" = true ]; then
    toggle_server_test_mode
elif [ "$EXTENSION_ONLY" = true ]; then
    toggle_extension_test_mode
else
    toggle_server_test_mode
    toggle_extension_test_mode
fi

print_header "SUMMARY"
echo "Test mode has been $([ "$ENABLE_TEST_MODE" = true ] && echo "ENABLED" || echo "DISABLED") for:"
if [ "$SERVER_ONLY" = true ]; then
    echo "- Server only"
elif [ "$EXTENSION_ONLY" = true ]; then
    echo "- Chrome extension only"
else
    echo "- Server"
    echo "- Chrome extension"
fi
echo ""

if [ "$ENABLE_TEST_MODE" = true ]; then
    echo "🧪 TEST MODE ACTIVE:"
    echo "  Environment: Test/Staging"
    echo "  Server URL: https://test.qubitrhythm.com"
    echo "  Stripe: Test mode (no real charges)"
    echo "  Database: Same as production (for compatibility)"
    echo "  Test Cards: 4242 4242 4242 4242 (any future exp, any CVC)"
    echo ""
    echo "Manual verification commands:"
    if [ "$SERVER_ONLY" = false ]; then
        echo "  Extension: Check .test-mode file exists in $EXTENSION_DIR"
        echo "  Extension: grep 'test.qubitrhythm.com' $EXTENSION_DIR/background.js"
        echo "  Extension: grep 'isTestMode = true' $EXTENSION_DIR/subscribe.js"
    fi
    if [ "$EXTENSION_ONLY" = false ]; then
        echo "  Server: kubectl get deployment cloud-function -n smartparent -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name==\"TEST_MODE\")].value}'"
        echo "  Server: kubectl get secret cloud-function-secrets -n smartparent -o jsonpath='{.data.SERVER_URL}' | base64 -d"
    fi
else
    echo "🚀 PRODUCTION MODE ACTIVE:"
    echo "  Environment: Production"
    echo "  Server URL: https://smartparent.qubitrhythm.com"
    echo "  Stripe: Live mode (REAL CHARGES WILL BE MADE)"
    echo "  Database: Production database"
    echo ""
    echo "Manual verification commands:"
    if [ "$SERVER_ONLY" = false ]; then
        echo "  Extension: Check .test-mode file does NOT exist in $EXTENSION_DIR"
        echo "  Extension: grep 'smartparent.qubitrhythm.com' $EXTENSION_DIR/background.js"
        echo "  Extension: grep 'isTestMode = false' $EXTENSION_DIR/subscribe.js"
    fi
    if [ "$EXTENSION_ONLY" = false ]; then
        echo "  Server: kubectl get deployment cloud-function -n smartparent -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name==\"TEST_MODE\")].value}'"
        echo "  Server: kubectl get secret cloud-function-secrets -n smartparent -o jsonpath='{.data.SERVER_URL}' | base64 -d"
    fi
fi

echo ""
echo "Note: You may need to wait a few moments for all changes to take effect."
if [ "$SERVER_ONLY" = false ] && [ "$EXTENSION_ONLY" = false ] || [ "$EXTENSION_ONLY" = true ]; then
    echo "Remember to reload the Chrome extension in chrome://extensions for changes to take effect."
fi
