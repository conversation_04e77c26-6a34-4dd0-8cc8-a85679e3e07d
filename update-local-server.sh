#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
NAMESPACE="smartparent"
DEPLOYMENT_NAME="cloud-function"
CONTAINER_NAME="cloud-function" # Name of the container within the deployment spec
LOCAL_IMAGE_NAME="cloud-function-local:latest"
DOCKERFILE_PATH="smartparent-k8s/cloud-function/Dockerfile" # Corrected path
BUILD_CONTEXT="smartparent-k8s/cloud-function" # Corrected path

# --- Functions ---
handle_error() {
    echo "Error occurred in script at line $1."
    exit 1
}
trap 'handle_error $LINENO' ERR

# --- Main Script ---
echo "Starting local update for $DEPLOYMENT_NAME..."

# 1. Build the local Docker image
echo "Building Docker image $LOCAL_IMAGE_NAME..."
docker build -t "$LOCAL_IMAGE_NAME" -f "$DOCKERFILE_PATH" "$BUILD_CONTEXT"
echo "Image built successfully."

# 2. Patch the deployment to use the local image and Never pull policy
#    This avoids modifying the main deployment YAML file.
echo "Patching deployment $DEPLOYMENT_NAME to use $LOCAL_IMAGE_NAME and imagePullPolicy: Never..."
kubectl patch deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"'"$CONTAINER_NAME"'","image":"'"$LOCAL_IMAGE_NAME"'","imagePullPolicy":"Never"}]}}}}'
echo "Deployment patched."

# 3. Trigger a rollout restart
echo "Restarting deployment $DEPLOYMENT_NAME..."
kubectl rollout restart deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE"
echo "Rollout triggered."

# 4. Wait for the rollout to complete
echo "Waiting for deployment rollout to finish..."
kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=300s # Increased timeout to 5 minutes
echo "Deployment $DEPLOYMENT_NAME updated successfully!"

# 5. Optional: Display pod status
echo "Current pods for $DEPLOYMENT_NAME:"
kubectl get pods -n "$NAMESPACE" -l app="$DEPLOYMENT_NAME" # Assuming deployment label matches name

echo "Local update process complete."
