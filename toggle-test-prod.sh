#!/bin/bash
# Script to toggle between production and test-production environments

set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
PROD_SERVER_DIR="smartparent-k8s"
TEST_PROD_SERVER_DIR="smartparent-k8s-test"
PROD_EXTENSION_DIR="smartparent-extension"
TEST_PROD_EXTENSION_DIR="smartparent-extension-test"

# --- Functions ---
handle_error() {
    echo "Error occurred in script at line $1."
    exit 1
}

trap 'handle_error $LINENO' ERR

print_header() {
    echo ""
    echo "===== $1 ====="
    echo ""
}

# --- Parse arguments ---
USE_TEST_PROD=false
SHOW_HELP=false
SERVER_ONLY=false
EXTENSION_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --test-prod)
            USE_TEST_PROD=true
            shift
            ;;
        --prod)
            USE_TEST_PROD=false
            shift
            ;;
        --server-only)
            SERVER_ONLY=true
            shift
            ;;
        --extension-only)
            EXTENSION_ONLY=true
            shift
            ;;
        --help|-h)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            SHOW_HELP=true
            shift
            ;;
    esac
done

if [ "$SHOW_HELP" = true ]; then
    echo "Usage: $0 [--test-prod|--prod] [--server-only|--extension-only]"
    echo ""
    echo "Options:"
    echo "  --test-prod        Use test-production environment"
    echo "  --prod             Use production environment (default)"
    echo "  --server-only      Only toggle server environment"
    echo "  --extension-only   Only toggle extension environment"
    echo "  --help, -h         Show this help message"
    exit 0
fi

# --- Set environment message ---
if [ "$USE_TEST_PROD" = true ]; then
    echo "Switching to test-production environment..."
else
    echo "Switching to production environment..."
fi

# --- Toggle server environment ---
toggle_server_environment() {
    print_header "SERVER CONFIGURATION"

    if [ "$USE_TEST_PROD" = true ]; then
        echo "Using test-production server directory: $TEST_PROD_SERVER_DIR"
        # Additional server-specific actions for test-production can be added here
    else
        echo "Using production server directory: $PROD_SERVER_DIR"
        # Additional server-specific actions for production can be added here
    fi

    echo "Server environment has been set to $([ "$USE_TEST_PROD" = true ] && echo "TEST-PRODUCTION" || echo "PRODUCTION")."
}

# --- Toggle extension environment ---
toggle_extension_environment() {
    print_header "EXTENSION CONFIGURATION"

    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        echo "Node.js is not installed or not in PATH. Skipping extension configuration."
        return
    fi

    if [ "$USE_TEST_PROD" = true ]; then
        # Check if the test-production extension directory exists
        if [ ! -d "$TEST_PROD_EXTENSION_DIR" ]; then
            echo "Extension directory $TEST_PROD_EXTENSION_DIR not found. Skipping extension configuration."
            return
        fi
        echo "Using test-production extension directory: $TEST_PROD_EXTENSION_DIR"
        # Additional extension-specific actions for test-production can be added here
    else
        # Check if the production extension directory exists
        if [ ! -d "$PROD_EXTENSION_DIR" ]; then
            echo "Extension directory $PROD_EXTENSION_DIR not found. Skipping extension configuration."
            return
        fi
        echo "Using production extension directory: $PROD_EXTENSION_DIR"
        # Additional extension-specific actions for production can be added here
    fi

    echo "Extension environment has been set to $([ "$USE_TEST_PROD" = true ] && echo "TEST-PRODUCTION" || echo "PRODUCTION")."
    echo "Note: You need to reload the extension in Chrome for changes to take effect."
}

# --- Main ---
if [ "$SERVER_ONLY" = true ]; then
    toggle_server_environment
elif [ "$EXTENSION_ONLY" = true ]; then
    toggle_extension_environment
else
    toggle_server_environment
    toggle_extension_environment
fi

print_header "SUMMARY"
echo "Environment has been set to $([ "$USE_TEST_PROD" = true ] && echo "TEST-PRODUCTION" || echo "PRODUCTION") for:"
if [ "$SERVER_ONLY" = true ]; then
    echo "- Server only"
elif [ "$EXTENSION_ONLY" = true ]; then
    echo "- Extension only"
else
    echo "- Server"
    echo "- Extension"
fi
echo ""
echo "Note: You may need to wait a few moments for all changes to take effect."
if [ "$SERVER_ONLY" = false ] && [ "$EXTENSION_ONLY" = false ] || [ "$EXTENSION_ONLY" = true ]; then
    echo "Remember to reload the Chrome extension in chrome://extensions for changes to take effect."
fi
