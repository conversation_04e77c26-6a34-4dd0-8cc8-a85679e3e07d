document.addEventListener('DOMContentLoaded', () => {
  // Force light mode
  document.documentElement.setAttribute('data-color-scheme', 'light');

  const errorMessage = document.getElementById('errorMessage');
  const subscribeButton = document.querySelector('.subscribe-button');
  const subscriptionOptions = document.querySelectorAll('.subscription-option');

  // Get the email from the URL query parameter and pre-fill the email input
  const urlParams = new URL(window.location.href).searchParams;
  const email = urlParams.get('email');
  if (email) {
    const emailInput = document.getElementById('emailInput');
    if (emailInput) {
      emailInput.value = email;
      console.log('Pre-filled email:', email);
    }
  }

  //import SERVER_URL from './constants.js';
  // Production version always uses production server URL
  const isTestMode = false; // Production mode
  const SERVER_URL = 'https://smartparent.qubitrhythm.com'; // Production server URL // Production server URL // Production server URL  // Production server URL

  console.log('Using server URL:', SERVER_URL, 'Test mode:', isTestMode);

  // Initialize the subscribe button's data-plan attribute based on the active subscription option
  const activeOption = document.querySelector('.subscription-option.active');
  if (activeOption) {
    const initialPlan = activeOption.dataset.plan;
    if (initialPlan) {
      subscribeButton.dataset.plan = initialPlan;
      console.log('Initial plan set to:', initialPlan);
    }
  }

  // Add test mode indicator to the page if in test mode
  if (isTestMode) {
    const testModeIndicator = document.createElement('div');
    testModeIndicator.style.position = 'fixed';
    testModeIndicator.style.top = '0';
    testModeIndicator.style.left = '0';
    testModeIndicator.style.width = '100%';
    testModeIndicator.style.backgroundColor = '#ffeb3b';
    testModeIndicator.style.color = '#000';
    testModeIndicator.style.padding = '5px';
    testModeIndicator.style.textAlign = 'center';
    testModeIndicator.style.zIndex = '9999';
    testModeIndicator.style.fontWeight = 'bold';
    testModeIndicator.textContent = 'TEST MODE - Using Stripe test environment - No real charges will be made';
    document.body.appendChild(testModeIndicator);

    // Add a small info button to show test card information
    const infoButton = document.createElement('button');
    infoButton.textContent = 'ⓘ';
    infoButton.style.marginLeft = '10px';
    infoButton.style.backgroundColor = 'transparent';
    infoButton.style.border = 'none';
    infoButton.style.cursor = 'pointer';
    infoButton.style.fontSize = '14px';
    infoButton.title = 'Test Card: 4242 4242 4242 4242\nExp: Any future date\nCVC: Any 3 digits\nZIP: Any 5 digits';
    testModeIndicator.appendChild(infoButton);

    // Show a tooltip with the price IDs when hovering over the info button
    infoButton.addEventListener('mouseover', () => {
      const tooltip = document.createElement('div');
      tooltip.style.position = 'absolute';
      tooltip.style.top = '30px';
      tooltip.style.left = '50%';
      tooltip.style.transform = 'translateX(-50%)';
      tooltip.style.backgroundColor = '#fff';
      tooltip.style.color = '#000';
      tooltip.style.padding = '10px';
      tooltip.style.borderRadius = '5px';
      tooltip.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
      tooltip.style.zIndex = '10000';
      tooltip.style.width = '300px';
      tooltip.style.textAlign = 'left';
      tooltip.innerHTML = `
        <p><strong>Test Price IDs:</strong></p>
        <p>Monthly ($2.99): price_1ROXMaAR7VlUIrExs1QtF2ma</p>
        <p>Annual ($29.99): price_1ROXIUAR7VlUIrExhmftEcT2</p>
        <p><strong>Production Price IDs:</strong></p>
        <p>Monthly ($2.99): price_1RP98oAR7VlUIrExatqrxAEE</p>
        <p>Annual ($29.99): price_1RP99PAR7VlUIrExQiPIcgZl</p>
      `;
      document.body.appendChild(tooltip);

      infoButton.addEventListener('mouseout', () => {
        document.body.removeChild(tooltip);
      }, { once: true });
    });
  }

  // Handle subscription option selection
  subscriptionOptions.forEach(option => {
    option.addEventListener('click', () => {
      console.log('Subscription option clicked:', option.dataset.plan);

      // Remove active class from all options
      subscriptionOptions.forEach(opt => opt.classList.remove('active'));

      // Add active class to the clicked option
      option.classList.add('active');

      // Update the subscribe button's data-plan attribute
      const selectedPlan = option.dataset.plan;
      console.log('Selected plan:', selectedPlan);

      // Make sure the plan is either 'monthly' or 'annual'
      if (selectedPlan !== 'monthly' && selectedPlan !== 'annual') {
        console.error('Invalid plan selected:', selectedPlan);
        console.log('Defaulting to monthly plan');
        subscribeButton.dataset.plan = 'monthly';
      } else {
        subscribeButton.dataset.plan = selectedPlan;
      }

      console.log('Button data-plan set to:', subscribeButton.dataset.plan);

      // Update button text based on selected plan
      if (subscribeButton.dataset.plan === 'annual') {
        subscribeButton.textContent = 'Subscribe Premium (Annual)';
      } else {
        subscribeButton.textContent = 'Subscribe Premium (Monthly)';
      }
    });
  });

  // Handle subscribe button click
  subscribeButton.addEventListener('click', async () => {
    // Get the plan from the button's data attribute
    let plan = subscribeButton.dataset.plan;
    const emailInput = document.getElementById('emailInput');
    const email = emailInput.value.trim();

    console.log('Subscribe button clicked with plan:', plan);
    console.log('Button data attributes:', JSON.stringify(subscribeButton.dataset));

    // Check if the active subscription option matches the button's plan
    const activeOption = document.querySelector('.subscription-option.active');
    if (activeOption) {
      console.log('Active option plan:', activeOption.dataset.plan);
    }

    // If plan is missing or invalid, try to get it from the active option or default to "monthly"
    if (!plan || (plan !== 'monthly' && plan !== 'annual')) {
        console.warn(`Button data-plan attribute is missing or invalid: "${plan}". Attempting to recover...`);

        // Try to get the plan from the active subscription option
        if (activeOption && activeOption.dataset.plan) {
            const activePlan = activeOption.dataset.plan;
            if (activePlan === 'monthly' || activePlan === 'annual') {
                console.log('Recovered plan from active option:', activePlan);
                subscribeButton.dataset.plan = activePlan;
                plan = activePlan;
            } else {
                console.warn(`Active option has invalid plan: "${activePlan}". Defaulting to monthly.`);
                subscribeButton.dataset.plan = 'monthly';
                plan = 'monthly';
            }
        } else {
            // Default to monthly as a last resort
            console.log('No active option found or it has no plan. Defaulting to monthly plan');
            subscribeButton.dataset.plan = 'monthly';
            plan = 'monthly';
        }
    }

    // Final validation check
    if (plan !== 'monthly' && plan !== 'annual') {
        console.error(`Plan is still invalid after recovery: "${plan}". Aborting checkout.`);
        showError('Could not determine subscription plan. Please try selecting a plan again or refresh the page.');
        return;
    }

    console.log('Final plan for checkout:', plan);

    if (!validateEmail(email)) {
      showError('Please enter a valid email address.');
      return;
    }

    try {
      // Show loading state
      subscribeButton.disabled = true;
      subscribeButton.textContent = 'Processing...';

      // Log the request details for debugging
      console.log('Sending subscription request:', {
        url: `${SERVER_URL}/create-checkout-session`,
        email: email,
        plan: plan
      });

      // In test mode, we'll simulate the checkout process
      // In production mode, we'll let the server determine the correct price ID
      // based on the plan name (monthly or annual)

      // We don't need to specify the price ID directly anymore
      // The server will use the correct price ID based on the plan name

      console.log('Using plan:', plan, 'in production mode');

      // Make the API call to the server
      console.log(`Making API call to ${SERVER_URL}/create-checkout-session`);
      console.log('Sending plan name:', plan);
      console.log('Client test mode: DISABLED');

      // Log the full request details for debugging
      // Use production price IDs
      const priceId = plan === 'monthly'
        ? 'price_1RP98oAR7VlUIrExatqrxAEE'  // Production monthly price ID
        : 'price_1RP99PAR7VlUIrExQiPIcgZl'; // Production annual price ID

      const requestBody = {
        email,
        plan, // Send the plan name (monthly or annual)
        testMode: false, // Production mode
        priceId: priceId // Directly specify the price ID
      };

      console.log('Full request details:', {
        url: `${SERVER_URL}/create-checkout-session`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const response = await fetch(`${SERVER_URL}/create-checkout-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Response status:', response.status);

      // Log response headers for debugging
      const responseHeaders = {};
      response.headers.forEach((value, name) => {
        responseHeaders[name] = value;
      });
      console.log('Response headers:', responseHeaders);

      const data = await response.json();
      console.log('Response data:', data);

      if (data.url) {
        // Redirect to the Stripe Checkout page
        console.log('Redirecting to Stripe checkout URL:', data.url);
        window.location.href = data.url;
      } else {
        // Show more detailed error information
        const errorMsg = data.error || 'An error occurred.';
        const errorDetails = data.details ? JSON.stringify(data.details) : '';
        console.error('Error from server:', errorMsg, errorDetails);

        // Show a more helpful error message to the user
        if (errorMsg === 'Invalid subscription plan selected') {
          showError('Invalid subscription plan selected. Please try again or contact support. Details: ' + errorDetails);
        } else {
          showError(errorMsg);
        }

        // Reset button state
        subscribeButton.disabled = false;
        subscribeButton.textContent = plan === 'annual' ? 'Subscribe Premium (Annual)' : 'Subscribe Premium (Monthly)';
      }
    } catch (error) {
      // Show more detailed error information
      console.error('Error during subscription process:', error);

      // Try to provide a more helpful error message based on the error
      if (error.message && error.message.includes('NetworkError')) {
        showError('Network error: Could not connect to the server. Please check your internet connection and try again.');
      } else if (error.message && error.message.includes('Failed to fetch')) {
        showError('Could not reach the server. Please check your internet connection and try again.');
      } else {
        showError('An error occurred: ' + error.message);
      }

      // Reset button state
      subscribeButton.disabled = false;
      subscribeButton.textContent = plan === 'annual' ? 'Subscribe Premium (Annual)' : 'Subscribe Premium (Monthly)';
    }
  });

  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
  }

  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  }

  // Add debug button functionality (hidden by default)
  const debugButton = document.getElementById('debugButton');
  const debugInfo = document.getElementById('debugInfo');
  const debugSection = document.querySelector('.debug-section');

  // Add keyboard shortcut to show debug section (Ctrl+Shift+D)
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      if (debugSection) {
        debugSection.style.display = debugSection.style.display === 'none' ? 'block' : 'none';
        console.log('Debug section toggled:', debugSection.style.display);
      }
    }
  });

  if (debugButton && debugInfo) {
    debugButton.addEventListener('click', async () => {
      try {
        debugButton.disabled = true;
        debugButton.textContent = 'Loading...';

        // Production mode only
        {
          try {
            // In production mode, fetch actual server configuration
            const response = await fetch(`${SERVER_URL}/debug-plans`);
            const data = await response.json();

            // Display the configuration
            debugInfo.innerHTML = `
              <h4>Production Server Configuration</h4>
              <p><strong>Server URL:</strong> ${SERVER_URL}</p>
              <p><strong>Stripe Mode:</strong> ${data.stripeMode || 'Unknown'}</p>
              <p><strong>Stripe Key Prefix:</strong> ${data.stripeKeyPrefix || 'Unknown'}</p>
              <p><strong>Test Monthly Price ID:</strong> ${data.TEST_PRICE_IDS?.monthly || 'Not set'}</p>
              <p><strong>Test Annual Price ID:</strong> ${data.TEST_PRICE_IDS?.annual || 'Not set'}</p>
              <p><strong>Production Monthly Price ID:</strong> ${data.PRODUCTION_PRICE_IDS?.monthly || 'Not set'}</p>
              <p><strong>Production Annual Price ID:</strong> ${data.PRODUCTION_PRICE_IDS?.annual || 'Not set'}</p>
              <p><strong>Client Test Mode:</strong> DISABLED</p>
              <p style="color: #d32f2f; font-weight: bold;">Production mode is active. Real charges will be made.</p>
            `;
          } catch (error) {
            console.error('Error fetching debug info:', error);
            debugInfo.innerHTML = `
              <h4>Production Server Configuration</h4>
              <p><strong>Server URL:</strong> ${SERVER_URL}</p>
              <p><strong>Client Test Mode:</strong> DISABLED</p>
              <p style="color: red;">Error fetching server details: ${error.message}</p>
              <p>Using hardcoded price IDs:</p>
              <p><strong>Test Monthly Price ID:</strong> price_1ROXMaAR7VlUIrExs1QtF2ma ($2.99/month)</p>
              <p><strong>Test Annual Price ID:</strong> price_1ROXIUAR7VlUIrExhmftEcT2 ($29.99/year)</p>
              <p><strong>Production Monthly Price ID:</strong> price_1RP98oAR7VlUIrExatqrxAEE ($2.99/month)</p>
              <p><strong>Production Annual Price ID:</strong> price_1RP99PAR7VlUIrExQiPIcgZl ($29.99/year)</p>
              <p style="color: #d32f2f; font-weight: bold;">Production mode is active. Real charges will be made.</p>
            `;
          }
        }

        debugInfo.style.display = 'block';
      } finally {
        debugButton.disabled = false;
        debugButton.textContent = 'Check Configuration';
      }
    });
  }
});
