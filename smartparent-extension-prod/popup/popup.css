/* CSS Variables for consistent styling */
:root {
    /* Primary Colors */
    --primary-color: #4285f4;
    --primary-light: #5a95f5;
    --primary-dark: #3367d6;
    --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

    /* Secondary Colors */
    --secondary-color: #5e35b1;
    --secondary-light: #7e57c2;
    --secondary-dark: #4527a0;
    --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

    /* Accent Colors */
    --accent-color: #f4b400;
    --accent-light: #ffc107;
    --accent-dark: #e69c00;
    --accent-gradient: linear-gradient(135deg, #f4b400 0%, #e69c00 100%);

    /* Status Colors */
    --success-color: #0f9d58;
    --success-light: #4caf50;
    --success-bg: rgba(15, 157, 88, 0.08);
    --error-color: #db4437;
    --error-light: #ef5350;
    --error-bg: rgba(219, 68, 55, 0.08);
    --warning-color: #f4b400;
    --info-color: #4285f4;

    /* Neutral Colors */
    --text-color: #202124;
    --text-light: #5f6368;
    --text-lighter: #80868b;
    --border-color: #dadce0;
    --border-light: #f1f3f4;

    /* Background Colors */
    --background-color: #e8f0fe;
    --container-bg: #f8f9fa;
    --hover-bg: rgba(66, 133, 244, 0.08);
    --premium-bg: #e8f0fe; /* Light blue background for Premium Features */
    --dialog-bg: #fef7e0;
    --modal-bg: #e6f4ea;
    --subscribe-bg: #fce8e6;

    /* UI Elements */
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
    --button-radius: 8px;
    --input-radius: 8px;

    /* Component-specific Colors */
    --premium-color: #5e35b1;
    --premium-gradient: linear-gradient(135deg, #5e35b1 0%, #7e57c2 100%);
    --button-primary: var(--primary-color);
    --button-secondary: var(--secondary-color);
    --button-accent: var(--accent-color);
}

/* Global Styles */
html, body {
    width: 350px; /* Further reduced from 360px */
    min-width: 350px; /* Further reduced from 360px */
    margin: 0;
    padding: 15px 0; /* Top and bottom padding only */
    font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--background-color);
    background-image:
        url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    color: var(--text-color);
    box-sizing: border-box;
    text-align: center;
    position: relative; /* Added for ribbon positioning */
}

/* Main sections layout - centered with no extra padding */
#authentication-section,
#trial-message,
.settings-section,
#subscribe-prompt {
    width: 92%; /* Slightly reduced from 94% */
    max-width: 320px; /* Reduced from 330px */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 14px; /* Consistent padding to maintain content readability */
    margin: 0 auto 12px; /* Maintained bottom margin */
    background-color: var(--container-bg);
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-light);
    position: relative; /* Ensure proper positioning */
    left: 0;
    right: 0;
}

/* Authentication section - remove conflicting padding */
#authentication-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 92%; /* Match updated width */
    max-width: 320px; /* Match updated max-width */
    padding: 14px; /* Match updated padding */
    margin: 0 auto 12px; /* Maintained margin */
    box-sizing: border-box;
}

/* Email input and activate button consistent widths */
#authentication-section #userEmail,
#authentication-section #activateButton {
    width: 92%; /* Match updated width */
    margin: 8px auto; /* Slightly increased margin for better spacing */
    display: block;
    padding: 10px; /* Consistent padding */
    box-sizing: border-box;
}

/* Reset other elements to not interfere */
input[type="number"],
.settings-section label {
    width: 92%; /* Match updated width */
    margin: 6px auto; /* Slightly increased margin for better spacing */
}

/* Welcome message style */
#trial-message {
    width: 92%; /* Match updated width */
    margin: 45px auto 12px; /* Further increased top margin for larger ribbon */
    padding: 12px; /* Consistent padding */
    background-color: #E3F2FD; /* Light blue background */
    border-radius: 4px;
    border: 1px solid #BBDEFB; /* Slightly darker blue border */
}

#trial-message p {
    width: 95%;
    margin: 8px auto;
    /* Default paragraph style within trial message */
    font-size: 1.1em; /* Keep existing size */
    line-height: 1.4; /* Keep existing line height */
}

/* Specific style for the main trial info text */
#trial-message .trial-info {
    color: #01579B; /* Darker blue text */
}

/* Styling for the countdown container paragraph */
#trial-message #countdown {
    font-size: 1em; /* Slightly smaller than main text */
    /* Color and weight will be set by inner spans */
}

/* Style for the "Remaining days: " prefix */
#trial-message #countdown .countdown-prefix {
    color: #01579B; /* Darker blue text, same as .trial-info */
    font-weight: normal; /* Ensure prefix is not bold */
}

/* Style for the actual time value */
#trial-message #countdown .countdown-time {
    color: #D32F2F; /* Keep red color */
    font-weight: bold; /* Make time value bold */
}

/* Form elements and inputs */
input[type="email"],
input[type="number"],
button,
.settings-section label {
    width: 92%; /* Match updated width */
    margin: 6px auto; /* Slightly increased margin for better spacing */
    display: block;
    padding: 10px; /* Consistent padding */
    box-sizing: border-box;
}

/* Input fields styling */
input[type="email"], /* Keep general email styling if needed elsewhere */
input[type="number"] {
    border: 1px solid var(--border-color);
    border-radius: var(--input-radius);
    font-size: 14px;
    padding: 10px; /* Consistent padding */
    transition: all 0.2s ease;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

input[type="email"]:focus,
input[type="number"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46,91,255,0.15);
    outline: none;
}

/* Button styling */
/* Specific styling for activate button - width handled above */
#activateButton {
    /* width: 95%; /* Removed, handled by the shared rule */
    padding: 12px;
    margin: 8px auto; /* Kept margin */
    display: block; /* Kept display */
    box-sizing: border-box; /* Kept box-sizing */
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--button-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#activateButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#saveSettings {
    width: 88%; /* Slightly reduced for better proportions */
    padding: 10px; /* Consistent padding */
    font-size: 15px; /* Maintained font size */
    background: var(--primary-gradient);
    color: white;
    border: none;
    cursor: pointer;
    border-radius: var(--button-radius);
    margin: 12px auto 8px; /* Maintained margins */
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#saveSettings:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Ribbon status styling */
#status-ribbon {
    position: fixed;
    top: 0;
    left: -13px; /* Moved further to the left to hide corner completely */
    z-index: 100;
    overflow: visible;
    width: 170px;
    height: 170px;
    pointer-events: none; /* Ensures clicks pass through to elements below */
}

#status-ribbon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 20px; /* Further reduced to 20px */
    background-color: rgba(255, 255, 255, 0.9);
    transform: rotate(-45deg) translateX(-35px) translateY(30px); /* Adjusted Y translation to keep bottom position */
    transform-origin: top left;
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
    z-index: -1;
}

#statusText {
    position: fixed;
    top: 15px;
    left: -20px; /* Adjusted to align better with the left side of the ribbon */
    transform: rotate(-45deg) translateX(-15px) translateY(3px); /* Adjusted Y translation to move text up by 2px */
    transform-origin: top left;
    font-size: 0.9em; /* Reduced font size to ensure all labels fit */
    font-weight: 700;
    text-align: bottom; /* Left-aligned text */
    padding: 6px 0 8px 15px; /* Increased bottom padding by 2px */
    width: 150px; /* Slightly wider to accommodate text */
    letter-spacing: 0.5px;
    color: white;
    z-index: 101; /* Higher than the ribbon */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);
    pointer-events: none; /* Ensures clicks pass through */
    display: flex; /* Add this */
    align-items: flex-end; /* Add this to align text to bottom */
    height: 33px; /* Set a fixed height for the text container */
    font-family: 'Arial', 'Helvetica Neue', sans-serif; /* More professional font */
}

/* Premium status */
#statusText.subscribed {
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    padding-left: 15px; /* Reduced padding to move text to the left side of the ribbon */
}

#statusText.subscribed ~ #status-ribbon::before {
    background-color: var(--secondary-color);
    background-image: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    border-bottom: 2px solid rgba(0, 77, 64, 0.3);
    opacity: 0.95;
    width: 200%; /* Match width with base ribbon */
    height: 20px; /* Further reduced to 20px */
    transform: rotate(-45deg) translateX(-35px) translateY(30px); /* Adjusted Y translation to keep bottom position */
}

/* Premium Trial status - special class added via JavaScript */
#statusText.trial {
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    font-size: 0.95em; /* Smaller font for Trial to ensure it fits */
    padding-left: 25px; /* Added left padding */
}

#statusText.trial ~ #status-ribbon::before {
    background-color: var(--accent-light);
    background-image: linear-gradient(135deg, var(--accent-light) 0%, var(--accent-dark) 100%);
    border-bottom: 2px solid rgba(193, 120, 23, 0.3);
    opacity: 0.95;
    width: 200%; /* Match width with other ribbons */
    height: 20px; /* Further reduced to 20px */
    transform: rotate(-45deg) translateX(-35px) translateY(30px); /* Adjusted Y translation to keep bottom position */
}

/* Standard/Unsubscribed status */
#statusText.unsubscribed {
    color: white; /* Changed from #333333 to white */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4); /* Updated shadow for better contrast with white text */
    font-weight: 700;
    font-size: 0.85em; /* Even smaller font for "Standard" to ensure it fits */
    padding-left: 15px; /* Reduced padding to move text to the left side of the ribbon */
}

#statusText.unsubscribed ~ #status-ribbon::before {
    background-color: var(--primary-color);
    background-image: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-bottom: 2px solid rgba(46, 91, 255, 0.3);
    opacity: 0.95;
    width: 200%; /* Match width with base ribbon */
    height: 20px; /* Further reduced to 20px */
    transform: rotate(-45deg) translateX(-35px) translateY(30px); /* Adjusted Y translation to keep bottom position */
}

/* Error status - special class added via JavaScript */
#statusText.error {
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    padding-left: 15px; /* Reduced padding to move text to the left side of the ribbon */
}

#statusText.error ~ #status-ribbon::before {
    background-color: var(--error-color);
    background-image: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);
    border-bottom: 2px solid rgba(219, 68, 55, 0.3);
    opacity: 0.95;
    width: 200%; /* Match width with base ribbon */
    height: 20px; /* Further reduced to 20px */
    transform: rotate(-45deg) translateX(-35px) translateY(30px); /* Adjusted Y translation to keep bottom position */
}

#status {
    color: var(--success-color);
    font-weight: 500;
}

/* Settings section specific styles */
.settings-section {
    margin-top: 20px;
    display: none; /* Hidden by default */
}

.settings-section h2 {
    width: 90%;  /* Match input width */
    margin: 0 auto 20px;  /* Center align with auto margins */
    color: var(--primary-color); /* Light blue color */
    font-weight: bold;
    text-align: center; /* Center the text */
    /* padding-left: 0; Removed as text-align: center handles alignment */
}

.settings-section label {
    color: var(--text-color);
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 5px;
}

/* Premium Features Section */
#premium-features-section {
    margin-top: 16px; /* Maintained from previous change */
    padding: 16px; /* Consistent padding */
    background-color: #e3f2fd; /* Explicit light blue color */
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
    border-radius: var(--border-radius);
    border: 1px solid rgba(66, 133, 244, 0.2);
    width: 92%; /* Match updated width */
    max-width: 320px; /* Match updated max-width */
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 12px; /* Maintained bottom margin */
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.premium-heading {
    color: var(--primary-color);
    font-size: 13px; /* Reduced from 16px to match "Save & Lock Settings" */
    margin: 0 auto 17px; /* Increased from 12px to 17px (added 5px) */
    text-align: center;
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    font-family: 'Arial', 'Helvetica Neue', sans-serif; /* More professional font */
}

/* Removed blue line under PREMIUM FEATURES */

/* Helper styles */
.buttonContainer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 10px 0;
    padding: 0;
}

#authMessage {
    width: 95%;
    text-align: center;
    color: red;
    margin: 8px auto;
}

.help-text {
    text-align: center;
    margin: 15px 0 10px 0;
    font-size: 13px;
    color: #666;
}

.help-text a {
    color: #007BFF;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

/* Footer styles */
footer {
    margin-top: 20px;
    font-size: 12px;
    text-align: center;
    color: #777;
}

footer a {
    color: #007BFF;
    text-decoration: none;
}

/* Premium Feature Items */
.premium-feature-item {
    width: 100%;
    margin: 10px auto 14px; /* Maintained margins */
    position: relative;
    padding: 0 4px; /* Slightly increased padding for better readability */
    box-sizing: border-box;
}

.checkbox-container {
    display: flex;
    align-items: flex-start; /* Align to top to handle text wrapping */
    gap: 0px; /* Maintained zero gap */
    margin-bottom: 10px; /* Maintained margin */
    padding-left: 4px; /* Increased from 2px for better readability */
    width: 100%;
}

.checkbox-container input[type="checkbox"] {
    width: auto !important;
    margin: 0;
    cursor: pointer;
    height: 18px;
    width: 18px !important;
    flex-shrink: 0;
    position: relative;
    left: 0;
    margin-top: 3px; /* Slightly adjusted to better align with text */
    margin-right: 5px; /* Add right margin to create space between checkbox and label */
}

.checkbox-container label {
    font-size: 14px;
    color: var(--text-color);
    white-space: normal; /* Allow text to wrap */
    margin: 0;
    padding: 0;
    cursor: pointer;
    line-height: 20px;
    font-weight: 500;
    flex: 1; /* Allow label to take remaining space */
    max-width: calc(100% - 25px); /* Ensure label doesn't extend beyond container */
    word-wrap: break-word; /* Ensure long words break and wrap */
}

.time-container {
    display: flex;
    align-items: center;
    gap: 2px; /* Further reduced from 4px to make elements even closer */
    margin-top: 8px;
    margin-left: 23px; /* Aligned with the checkbox position */
    width: calc(100% - 23px);
}

.time-container input[type="time"] {
    width: 100px !important; /* Further reduced from 110px */
    padding: 8px 8px; /* Reduced horizontal padding from 10px to 8px */
    border: 1px solid var(--border-color);
    border-radius: var(--input-radius);
    color: var(--text-color);
    font-size: 14px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.03);
}

.time-container input[type="time"]::-webkit-calendar-picker-indicator {
    margin-left: 2px; /* Reduced from 4px */
    padding: 0; /* Remove any padding */
}

.time-container input[type="time"]::-webkit-datetime-edit-ampm-field {
    display: none;
}

.time-container input[type="time"]::-webkit-clear-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

#amPmDisplay {
    margin-left: 0px; /* Reduced from 4px */
    font-size: 13px;
    color: #666;
    min-width: 26px;
}

.checkbox-container label[for="emailHistoryEnabled"]::after,
.checkbox-container label[for="customWhitelistEnabled"]::after {
    content: "";
}

/* Dialog Overlay */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none; /* Hidden by default */
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.dialog-content {
    background-color: var(--dialog-bg);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='88' height='24' viewBox='0 0 88 24'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23f4b400' fill-opacity='0.15'%3E%3Cpath d='M13 0v8H0V0h13zm13 0v8H13V0h13zM0 8v8h13V8H0zm13 8v8h13v-8H13zM0 24h13v-8H0v8zm27-8v8h13v-8H27zm13 8h13v-8H40v8zm13-8v8h13v-8H53zm13 8h13v-8H66v8zm13-8v8h9v-8h-9zm-13 0v-8H53v8h13zm-13-8v-8H40v8h13zm-13-8v8h13v-8H40zm-13 8v-8H14v8h13zM27 8v-8h-9v8h9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    padding: 16px; /* Consistent padding */
    border-radius: var(--border-radius);
    width: 92%; /* Match updated width */
    max-width: 320px; /* Match updated max-width */
    box-shadow: 0 10px 25px rgba(0,0,0,0.1), 0 2px 10px rgba(0,0,0,0.05);
    animation: dialogFadeIn 0.3s ease;
    border: 1px solid rgba(244, 180, 0, 0.2);
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dialog-content h2 {
    color: var(--secondary-color);
    font-size: 18px;
    margin: 0 0 15px 0;
    text-align: center;
    line-height: 1.3;
    font-weight: 600;
}

.input-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
}

#websiteInput {
    flex: 1;
    padding: 0 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--input-radius);
    font-size: 13px;
    height: 36px;
    line-height: normal;
    box-sizing: border-box;
    transition: all 0.2s ease;
}

#websiteInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46,91,255,0.15);
    outline: none;
}

#addWebsite {
    padding: 0 15px;
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: var(--button-radius);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    width: auto !important;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    margin: 0;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#addWebsite:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.whitelist-entries {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.5);
}

.whitelist-entry {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    background-color: var(--container-bg);
    border-radius: var(--border-radius);
    margin-bottom: 6px;
    font-size: 13px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
}

.whitelist-entry:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.whitelist-entry span {
    flex: 1;
    margin-right: 8px;
    word-break: break-all;
    color: var(--text-color);
}

.whitelist-entry:last-child {
    margin-bottom: 0;
}

.delete-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--button-radius);
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    width: 60px !important;
    min-width: 60px !important;
    margin: 0 !important;
    height: 26px;
    line-height: 18px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.delete-btn:hover {
    background-color: var(--error-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.button-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

#cancelButton,
#saveButton {
    padding: 0;
    border: none;
    border-radius: var(--button-radius);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    width: 90px !important;
    height: 36px;
    line-height: 36px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#cancelButton {
    background-color: #f5f7fa;
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

#cancelButton:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#saveButton {
    background: var(--primary-gradient);
    color: white;
}

#saveButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: var(--modal-bg);
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%230f9d58' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
    padding: 16px; /* Consistent padding */
    font-size: 14px;
    color: var(--text-color);
    border-radius: var(--border-radius);
    text-align: center;
    width: 92%; /* Match updated width */
    max-width: 320px; /* Match updated max-width */
    box-shadow: 0 10px 25px rgba(0,0,0,0.1), 0 2px 10px rgba(0,0,0,0.05);
    transform: translateY(-20px);
    transition: all 0.3s ease;
    border: 1px solid rgba(15, 157, 88, 0.2);
}

.modal.show {
    display: flex; /* Show the modal */
    opacity: 1;
}

.modal.show .modal-content {
    transform: translateY(0);
}

/* Modal buttons styling */
.modal-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
}

.modal-buttons button {
    padding: 10px;
    border: none;
    border-radius: var(--button-radius);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#modalStartTrialBtn {
    background: var(--primary-gradient);
    color: white;
    border: 1px solid rgba(66, 133, 244, 0.2);
}

#modalSubscribeBtn {
    background: var(--secondary-gradient);
    color: white;
    border: 1px solid rgba(94, 53, 177, 0.2);
}

#modalCancelBtn {
    background-color: #f5f7fa;
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

.modal-buttons button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Subscribe Prompt Styling */
#subscribe-prompt {
    display: none; /* Hidden by default, shown by JS */
    background-color: var(--subscribe-bg); /* Pink background */
    background-image: url("data:image/svg+xml,%3Csvg width='44' height='12' viewBox='0 0 44 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 12v-2L0 0v10l4 2h16zm18 0l4-2V0L22 10v2h16zM20 0v8L4 0h16zm18 0L22 8V0h16z' fill='%23db4437' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
    border: 1px solid rgba(219, 68, 55, 0.2);
}

#subscribe-prompt p {
    margin: 0; /* Remove default paragraph margin */
    font-size: 1em; /* Adjust font size if needed */
    line-height: 1.5;
    color: var(--text-color);
}

#subscribe-prompt a {
    color: var(--error-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    padding: 2px 5px;
    border-radius: 4px;
}

#subscribe-prompt a:hover {
    background-color: rgba(219, 68, 55, 0.1);
    text-decoration: none;
}
