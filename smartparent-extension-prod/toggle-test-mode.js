/**
 * <PERSON><PERSON><PERSON> to toggle test mode for the Chrome extension
 * 
 * This script updates the necessary files in the Chrome extension to switch between
 * test mode and production mode.
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
let enableTestMode = false;
let showHelp = false;

for (const arg of args) {
  if (arg === '--enable') {
    enableTestMode = true;
  } else if (arg === '--disable') {
    enableTestMode = false;
  } else if (arg === '--help' || arg === '-h') {
    showHelp = true;
  } else {
    console.error(`Unknown option: ${arg}`);
    showHelp = true;
  }
}

if (showHelp) {
  console.log('Usage: node toggle-test-mode.js [--enable|--disable]');
  console.log('');
  console.log('Options:');
  console.log('  --enable    Enable test mode');
  console.log('  --disable   Disable test mode (default)');
  console.log('  --help, -h  Show this help message');
  process.exit(0);
}

// Configuration
const testServerUrl = 'https://test.qubitrhythm.com'; // Test server URL
const prodServerUrl = 'https://smartparent.qubitrhythm.com'; // Production server URL

const filesToUpdate = [
  {
    path: 'background.js',
    testModeUpdates: [
      {
        search: /const SERVER_URL = ['"]https:\/\/smartparent\.qubitrhythm\.com['"];/,
        replace: `const SERVER_URL = '${testServerUrl}'; // Test mode`,
        description: 'SERVER_URL'
      },
      {
        search: /const uninstallUrl = ['"]https:\/\/smartparent\.qubitrhythm\.com\/staticHosting\/uninstall_survey\.html['"];/,
        replace: `const uninstallUrl = '${testServerUrl}/staticHosting/uninstall_survey.html'; // Test mode`,
        description: 'uninstallUrl'
      }
    ],
    prodModeUpdates: [
      {
        search: /const SERVER_URL = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const SERVER_URL = '${prodServerUrl}';`,
        description: 'SERVER_URL'
      },
      {
        search: /const uninstallUrl = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const uninstallUrl = '${prodServerUrl}/staticHosting/uninstall_survey.html';`,
        description: 'uninstallUrl'
      }
    ]
  },
  {
    path: 'popup/popup.js',
    testModeUpdates: [
      {
        search: /const SERVER_URL = ['"]https:\/\/smartparent\.qubitrhythm\.com['"];/,
        replace: `const SERVER_URL = '${testServerUrl}'; // Test mode`,
        description: 'SERVER_URL'
      }
    ],
    prodModeUpdates: [
      {
        search: /const SERVER_URL = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const SERVER_URL = '${prodServerUrl}';`,
        description: 'SERVER_URL'
      }
    ]
  },
  {
    path: 'subscribe.js',
    testModeUpdates: [
      {
        search: /const isTestMode = false;.*\/\/ Production mode/,
        replace: `const isTestMode = true; // Test mode`,
        description: 'isTestMode'
      },
      {
        search: /const SERVER_URL = ['"]https:\/\/smartparent\.qubitrhythm\.com['"];/,
        replace: `const SERVER_URL = '${testServerUrl}'; // Test mode`,
        description: 'SERVER_URL'
      }
    ],
    prodModeUpdates: [
      {
        search: /const isTestMode = true;.*\/\/ Test mode/,
        replace: `const isTestMode = false; // Production mode`,
        description: 'isTestMode'
      },
      {
        search: /const SERVER_URL = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const SERVER_URL = '${prodServerUrl}'; // Production server URL`,
        description: 'SERVER_URL'
      }
    ]
  }
];

// Function to update a file
function updateFile(filePath, updates) {
  if (!fs.existsSync(filePath)) {
    console.warn(`    ⚠ File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  let changes = [];
  
  for (const update of updates) {
    if (content.match(update.search)) {
      const oldMatch = content.match(update.search)[0];
      content = content.replace(update.search, update.replace);
      updated = true;
      changes.push({
        description: update.description,
        from: oldMatch.trim(),
        to: update.replace.trim()
      });
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`    ✓ ${path.basename(filePath)} updated`);
    changes.forEach(change => {
      console.log(`      - ${change.description}: "${change.from}" → "${change.to}"`);
    });
  } else {
    console.log(`    - ${path.basename(filePath)}: No changes needed`);
  }
}

// Main function
function main() {
  console.log(`${enableTestMode ? 'Enabling' : 'Disabling'} test mode...`);
  
  for (const file of filesToUpdate) {
    const filePath = path.resolve(__dirname, file.path);
    const updates = enableTestMode ? file.testModeUpdates : file.prodModeUpdates;
    
    console.log(`  Updating ${file.path}:`);
    updateFile(filePath, updates);
  }
  
  // Create or remove test mode indicator file
  const testModeIndicatorPath = path.resolve(__dirname, '.test-mode');
  if (enableTestMode) {
    fs.writeFileSync(testModeIndicatorPath, 'true');
    console.log('  ✓ Created test mode indicator file (.test-mode)');
  } else {
    if (fs.existsSync(testModeIndicatorPath)) {
      fs.unlinkSync(testModeIndicatorPath);
      console.log('  ✓ Removed test mode indicator file (.test-mode)');
    }
  }
  
  console.log(`Test mode has been ${enableTestMode ? 'ENABLED' : 'DISABLED'}.`);
  console.log('Note: You need to reload the extension in Chrome for changes to take effect.');
  
  if (enableTestMode) {
    console.log('');
    console.log('Test mode configuration:');
    console.log(`  - Server URL: ${testServerUrl}`);
    console.log('  - Stripe: Test mode (no real charges)');
    console.log('  - Test cards: 4242 4242 4242 4242 (any future exp, any CVC)');
  } else {
    console.log('');
    console.log('Production mode configuration:');
    console.log(`  - Server URL: ${prodServerUrl}`);
    console.log('  - Stripe: Live mode (REAL CHARGES WILL BE MADE)');
  }
}

// Run the script
main();
