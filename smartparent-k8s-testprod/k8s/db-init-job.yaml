apiVersion: batch/v1
kind: Job
metadata:
  name: db-init-test
  namespace: smartparent-test
spec:
  template:
    metadata:
      namespace: smartparent-test
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:14.9
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: PGHOST
          value: postgres-test
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_PASSWORD
        command:
        - sh
        - -c
        - |
          until pg_isready -h postgres-test; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
      containers:
      - name: db-init
        image: postgres:14.9
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: PGHOST
          value: postgres-test
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_PASSWORD
        - name: PGDATABASE
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_DB
        command:
        - sh
        - -c
        - |
          echo "Starting database initialization..."
          if psql -f /scripts/db-init.sql; then
            echo "Database initialization completed successfully"
            exit 0
          else
            echo "Database initialization failed"
            exit 1
          fi
        volumeMounts:
        - name: db-init-scripts
          mountPath: /scripts
      volumes:
      - name: db-init-scripts
        configMap:
          name: db-init-configmap-test
      restartPolicy: Never
  backoffLimit: 4
