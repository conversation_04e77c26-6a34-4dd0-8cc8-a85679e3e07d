apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-function-test
  namespace: smartparent-test
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cloud-function-test
  template:
    metadata:
      labels:
        app: cloud-function-test
    spec:
      containers:
      - name: cloud-function-test
        image: gcr.io/smartparent/cloud-function-test:latest
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /check
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /check
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        env:
        - name: SERVER_URL
          value: "http://*************"
        - name: TEST_SERVER_URL
          value: "http://*************"
        - name: NODE_ENV
          value: "production"
        - name: TEST_MODE
          value: "true"  # Set to "true" for test mode, "false" for production
        - name: PORT
          value: "8080"
        - name: CHECK_ENDPOINT
          value: "/check"
        - name: TIMEOUT
          value: "5000"
        - name: DATABASE_URL
          value: "postgres://postgres:<EMAIL>:5432/smartparent-test-db"
        - name: DB_SSL
          value: "false"
        - name: PGHOST
          value: "postgres-test.smartparent-test.svc.cluster.local"
        - name: PGPORT
          value: "5432"
        - name: PGDATABASE
          value: "smartparent-test-db"
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets-test
              key: POSTGRES_PASSWORD
        - name: PLAN_PRICE_IDS_STANDARD
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: PLAN_PRICE_IDS_STANDARD
        - name: PLAN_PRICE_IDS_MONTHLY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: PLAN_PRICE_IDS_MONTHLY
        - name: PLAN_PRICE_IDS_ANNUAL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: PLAN_PRICE_IDS_ANNUAL
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: STRIPE_SECRET_KEY
        - name: GROK_API_URL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: GROK_API_URL
        - name: GROK_API_KEY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: GROK_API_KEY
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: STRIPE_WEBHOOK_SECRET
        - name: FROM_EMAIL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: FROM_EMAIL
        - name: EMAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: EMAIL_PASSWORD
        - name: SMTP_HOST
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: SMTP_HOST
        - name: SMTP_PORT
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: SMTP_PORT
        - name: SMTP_SECURE
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets-test
              key: SMTP_SECURE
        # - name: FIREBASE_PROJECT_ID # Removed as Firebase is no longer used
        #   valueFrom:
        #     secretKeyRef:
        #       name: cloud-function-secrets
        #       key: FIREBASE_PROJECT_ID
