apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - selector:
        dnsZones:
          - "qubitrhythm.com"
      dns01:
        cloudDNS:
          project: smartparent
          hostedZoneName: smartparent-zone
          serviceAccountSecretRef:
            name: clouddns-service-account
            key: credentials.json
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: smartparent-test-tls
  namespace: smartparent-test
spec:
  secretName: smartparent-test-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - "*.test.qubitrhythm.com"  # Wildcard certificate for all subdomains
  - test.qubitrhythm.com      # Base domain
  - extension-test.qubitrhythm.com
  usages:
    - server auth
    - client auth
