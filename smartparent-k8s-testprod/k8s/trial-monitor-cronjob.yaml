apiVersion: batch/v1
kind: CronJob
metadata:
  name: smartparent-trial-monitor
spec:
  # Schedule to run daily at 3:00 AM UTC
  # Adjust as needed: https://crontab.guru/
  schedule: "0 3 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trial-monitor
            # IMPORTANT: Replace YOUR_GCP_PROJECT_ID with your actual GCP project ID
            image: gcr.io/YOUR_GCP_PROJECT_ID/smartparent-trial-monitor:latest
            imagePullPolicy: Always # Ensure the latest image is pulled
            env:
            # Database Credentials from postgres-secrets
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_USER
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_DB
            # Assuming POSTGRES_HOST and POSTGRES_PORT are defaulted or use service discovery
            # Add them here if they are explicitly set in secrets or needed
            # - name: POSTGRES_HOST
            #   value: "postgres-service" # Or from secret if defined
            # - name: POSTGRES_PORT
            #   value: "5432" # Or from secret if defined

            # Test mode configuration
            - name: TEST_MODE
              value: "false"  # Set to "true" for test mode, "false" for production

            # Email and Server URL Credentials from cloud-function-secrets
            # Ensure these keys exist in your cloud-function-secrets
            - name: FROM_EMAIL
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets # Or cloud-function-secrets-test? Verify correct secret name
                  key: FROM_EMAIL
            - name: EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets # Or cloud-function-secrets-test? Verify correct secret name
                  key: EMAIL_PASSWORD
            - name: SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets # Or cloud-function-secrets-test? Verify correct secret name
                  key: SERVER_URL
            # Add SMTP settings if they differ from defaults and are in secrets
            # - name: SMTP_HOST
            #   valueFrom:
            #     secretKeyRef:
            #       name: cloud-function-secrets
            #       key: SMTP_HOST
            # - name: SMTP_PORT
            #   valueFrom:
            #     secretKeyRef:
            #       name: cloud-function-secrets
            #       key: SMTP_PORT
            # - name: SMTP_SECURE
            #   valueFrom:
            #     secretKeyRef:
            #       name: cloud-function-secrets
            #       key: SMTP_SECURE # Should be 'true' or 'false' as a string

          restartPolicy: OnFailure # Restart the job container if it fails
      backoffLimit: 1 # Number of retries before marking job as failed
  concurrencyPolicy: Forbid # Prevents multiple jobs from running concurrently if one takes too long
  successfulJobsHistoryLimit: 3 # Keep history of last 3 successful jobs
  failedJobsHistoryLimit: 1 # Keep history of last 1 failed job
