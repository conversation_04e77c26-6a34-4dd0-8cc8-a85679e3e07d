/**
 * Global Configuration Module for Test-Production Environment
 *
 * This module provides a centralized configuration system for the test-production environment.
 * It uses the test server URL but operates in production mode.
 */

require('dotenv').config();

// For test-production, we want to use test mode with test server URL
const TEST_MODE = true; // Always use test mode for test-production

// Force using test price IDs regardless of TEST_MODE
const FORCE_TEST_PRICE_IDS = true;

// Configuration object with getters that return appropriate values
const config = {
  // Test mode flag - always true for test-production
  isTestMode: TEST_MODE,

  // Server URL - always use test server URL
  get serverUrl() {
    return process.env.TEST_SERVER_URL || 'https://test.qubitrhythm.com';
  },

  // Database configuration
  get databaseUrl() {
    return TEST_MODE
      ? (process.env.TEST_DATABASE_URL || process.env.DATABASE_URL)
      : process.env.DATABASE_URL;
  },

  get dbSsl() {
    return process.env.DB_SSL === 'true';
  },

  // Stripe configuration
  get stripeSecretKey() {
    return TEST_MODE
      ? (process.env.TEST_STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY)
      : process.env.STRIPE_SECRET_KEY;
  },

  get stripeWebhookSecret() {
    return TEST_MODE
      ? (process.env.TEST_STRIPE_WEBHOOK_SECRET || process.env.STRIPE_WEBHOOK_SECRET)
      : process.env.STRIPE_WEBHOOK_SECRET;
  },

  // Plan price IDs
  get planPriceIdsMonthly() {
    // Always use test price IDs for test-production environment
    return 'price_1ROXMaAR7VlUIrExs1QtF2ma'; // Test monthly price ID
  },

  get planPriceIdsAnnual() {
    // Always use test price IDs for test-production environment
    return 'price_1ROXIUAR7VlUIrExhmftEcT2'; // Test annual price ID
  },

  get planPriceIdsStandard() {
    return TEST_MODE
      ? (process.env.TEST_PLAN_PRICE_IDS_STANDARD || process.env.PLAN_PRICE_IDS_STANDARD)
      : process.env.PLAN_PRICE_IDS_STANDARD;
  },

  // Email configuration
  get fromEmail() {
    return process.env.FROM_EMAIL;
  },

  get emailPassword() {
    return process.env.EMAIL_PASSWORD;
  },

  get smtpHost() {
    return process.env.SMTP_HOST || 'smtp.gmail.com';
  },

  get smtpPort() {
    return process.env.SMTP_PORT || 465;
  },

  get smtpSecure() {
    return process.env.SMTP_SECURE === 'true';
  },

  // API configuration
  get grokApiUrl() {
    return process.env.GROK_API_URL;
  },

  get grokApiKey() {
    return process.env.GROK_API_KEY;
  },

  // Other configuration
  get timeout() {
    return process.env.TIMEOUT || 8000;
  },

  get port() {
    return process.env.PORT || 8080;
  },

  get maxRetries() {
    return 2;
  },

  get retryDelay() {
    return 1000;
  },

  // Helper method to get all price IDs
  getPlanPriceIds() {
    return {
      standard: this.planPriceIdsStandard,
      monthly: this.planPriceIdsMonthly,
      annual: this.planPriceIdsAnnual,
    };
  },

  // Helper method to get test price IDs
  getTestPriceIds() {
    return {
      monthly: 'price_1ROXMaAR7VlUIrExs1QtF2ma', // Test monthly price ID
      annual: 'price_1ROXIUAR7VlUIrExhmftEcT2'   // Test annual price ID
    };
  },

  // Helper method to get production price IDs
  // For test-production, we always use test price IDs even for "production" mode
  // because we're using a test Stripe key
  getProductionPriceIds() {
    // Always return test price IDs for test-production environment
    return this.getTestPriceIds();
  }
};

module.exports = config;
