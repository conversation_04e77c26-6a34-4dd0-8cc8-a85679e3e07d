apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cloud-function-ingress-test
  namespace: smartparent-test
  annotations:
    cert-manager.io/cluster-issuer: selfsigned-issuer # Changed from letsencrypt-prod for local testing
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # cert-manager.io/acme-challenge-type: http01 # Removed for self-signed issuer
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-origin: "chrome-extension://alaofgjjepmmoimjncpmpdbgpofphdgl, chrome-extension://*, https://smartparent.qubitrhythm.com, http://localhost:*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - test.qubitrhythm.com
    - extension-test.qubitrhythm.com
    secretName: smartparent-test-tls
  rules:
  - host: test.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function-test
            port:
              number: 80
  - host: extension-test.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function-test
            port:
              number: 80
