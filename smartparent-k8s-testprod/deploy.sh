#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in deploy script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details for test-production environment
PROJECT_ID="smartparent"
CLUSTER_NAME="smartparent-k8s-test"
ZONE="us-central1-f"
REGION="us-central1"
NAMESPACE="smartparent-test"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Delete existing cluster if it exists
#echo "Checking for existing cluster..."
#if gcloud container clusters list --filter="name=$CLUSTER_NAME" --format="get(name)" | grep -q "^$CLUSTER_NAME$"; then
#    echo "Deleting existing cluster..."
#    gcloud container clusters delete $CLUSTER_NAME --zone $ZONE --quiet
#fi

# Create new cluster with proper configuration
#echo "Creating new GKE cluster..."
#gcloud container clusters create $CLUSTER_NAME \
#    --zone $ZONE \
#    --num-nodes 2 \
#    --machine-type e2-standard-2 \
#    --disk-size 50 \
#    --enable-ip-alias \
#    --release-channel regular \
#    --workload-pool=$PROJECT_ID.svc.id.goog

# Get cluster credentials
echo "Getting cluster credentials..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE

# Create namespace if it doesn't exist
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations with proper order and verification
echo "Applying Kubernetes configurations..."

echo "Creating secrets..."
kubectl apply -f k8s/postgres-secrets.yaml -n $NAMESPACE

echo "Creating custom StorageClass with immediate binding..."
kubectl apply -f k8s/immediate-binding-storageclass.yaml

echo "Creating storage..."
kubectl apply -f k8s/postgres-pvc.yaml -n $NAMESPACE

echo "Deploying PostgreSQL..."
kubectl apply -f k8s/postgres-deployment.yaml -n $NAMESPACE
kubectl apply -f k8s/postgres-service.yaml -n $NAMESPACE



echo "Checking if PVC is bound..."
PVC_STATUS=""
MAX_RETRIES=30
RETRY_COUNT=0

while [ "$PVC_STATUS" != "Bound" ] && [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    PVC_STATUS=$(kubectl get pvc postgres-pvc-test -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")

    if [ "$PVC_STATUS" == "Bound" ]; then
        echo "PVC is bound successfully!"
        break
    fi

    echo "PVC status: $PVC_STATUS. Waiting for it to be bound... (Attempt $RETRY_COUNT/$MAX_RETRIES)"
    RETRY_COUNT=$((RETRY_COUNT + 1))
    sleep 5
done

if [ "$PVC_STATUS" != "Bound" ]; then
    echo "PVC binding failed or timed out - checking status:"
    kubectl describe pvc/postgres-pvc-test -n $NAMESPACE
    echo "Continuing despite PVC binding issue..."
fi




# Wait for PostgreSQL pod to be ready
echo "Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres-test -n $NAMESPACE --timeout=180s

echo "Initializing database..."
kubectl apply -f k8s/db-init-configmap.yaml -n $NAMESPACE
kubectl apply -f k8s/db-init-job.yaml -n $NAMESPACE

# Wait for database initialization to complete
echo "Waiting for database initialization..."
kubectl wait --for=condition=complete job/db-init-test -n $NAMESPACE --timeout=300s

echo "PostgreSQL deployment completed successfully!"

# Build and push cloud function image for test-production
echo "Building cloud function Docker image for test-production..."
docker build -t gcr.io/$PROJECT_ID/cloud-function-test:latest -f cloud-function/Dockerfile cloud-function

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing cloud function image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/cloud-function-test:latest

echo "Deploying cloud function to test-production environment..."
kubectl apply -f k8s/cloud-function-secrets.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-deployment.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-service.yaml -n $NAMESPACE

# Wait for cloud function deployment to be ready
echo "Waiting for cloud function to be ready..."
kubectl wait --for=condition=available deployment/cloud-function-test -n $NAMESPACE --timeout=180s


# Check if cert-manager is installed
echo "Checking if cert-manager is installed..."
if kubectl get crd certificates.cert-manager.io &> /dev/null; then
    echo "cert-manager is already installed."
else
    echo "cert-manager is not installed. Installing..."
    kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml

    # Wait for cert-manager to be ready
    echo "Waiting for cert-manager to be ready..."
    kubectl wait --for=condition=available deployment/cert-manager -n cert-manager --timeout=300s
    kubectl wait --for=condition=available deployment/cert-manager-cainjector -n cert-manager --timeout=300s
    kubectl wait --for=condition=available deployment/cert-manager-webhook -n cert-manager --timeout=300s
fi

# Apply the self-signed issuer
echo "Applying self-signed issuer..."
kubectl apply -f k8s/selfsigned-issuer.yaml

# Wait for the certificate to be ready
echo "Waiting for certificate to be ready..."
sleep 10  # Give cert-manager some time to process the certificate

# Apply the ingress
echo "Applying ingress..."
kubectl apply -f k8s/cloud-function-ingress.yaml

# Verify all deployments
echo "Verifying deployments..."
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE
kubectl get deployments -n $NAMESPACE

echo "All deployments completed successfully!"
echo "Cloud function for test-production environment will be available at the LoadBalancer IP address shown above."
echo "The server will be accessible at https://test.qubitrhythm.com"
