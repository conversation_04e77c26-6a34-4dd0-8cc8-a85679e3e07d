#!/bin/bash

# Copy the updated config.js file to the container
echo "Copying updated config.js to the container..."
kubectl cp cloud-function/config.js smartparent-test/$(kubectl get pods -n smartparent-test -l app=cloud-function-test -o jsonpath='{.items[0].metadata.name}'):/app/config.js

# Restart the deployment to pick up the changes
echo "Restarting the deployment..."
kubectl rollout restart deployment -n smartparent-test cloud-function-test

# Wait for the deployment to complete
echo "Waiting for the deployment to complete..."
kubectl rollout status deployment -n smartparent-test cloud-function-test

echo "Done! The Stripe price IDs should now be fixed."
echo "You can check the logs with: kubectl logs -n smartparent-test <pod-name> --tail=100"
