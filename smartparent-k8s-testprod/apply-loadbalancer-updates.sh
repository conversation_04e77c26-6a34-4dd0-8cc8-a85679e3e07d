#!/bin/bash

# Apply the updated YAML files
echo "Applying updated service configuration (LoadBalancer)..."
kubectl apply -f k8s/cloud-function-service-updated.yaml

echo "Applying updated ingress configuration..."
kubectl apply -f k8s/cloud-function-ingress-updated.yaml

echo "Applying updated deployment configuration..."
kubectl apply -f k8s/cloud-function-deployment-updated.yaml

# Wait for the LoadBalancer to get an external IP
echo "Waiting for LoadBalancer to get an external IP..."
external_ip=""
while [ -z $external_ip ]; do
    echo "Waiting for external IP..."
    external_ip=$(kubectl get svc -n smartparent-test cloud-function-test --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
    [ -z "$external_ip" ] && sleep 10
done

echo "LoadBalancer external IP: $external_ip"

# Update DNS record for test.qubitrhythm.com
echo "Please update your DNS record for test.qubitrhythm.com to point to: $external_ip"

# Restart the pods to pick up the new configuration
echo "Restarting the pods to pick up the new configuration..."
kubectl rollout restart deployment -n smartparent-test cloud-function-test

echo "Done! Please wait a few minutes for the changes to propagate."
echo "You can test the connection with: curl -k -v https://test.qubitrhythm.com/check"
echo "Or with the LoadBalancer IP: curl -v http://$external_ip/check"
