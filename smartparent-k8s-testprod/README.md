# SmartParent Test-Production Environment

This directory contains a modified version of the SmartParent server code configured for a test-production environment. This environment is designed to test the production server configuration without affecting existing users on the actual production environment.

## Overview

The test-production environment:
- Uses the test server URL (`https://test.qubitrhythm.com`)
- Operates in production mode (not test mode)
- Uses production price IDs for Stripe subscriptions
- Uses test Stripe keys for payment processing
- Runs in a separate Kubernetes namespace (`smartparent-test`)

## Key Differences from Production

1. **Server URL**: Always uses `https://test.qubitrhythm.com` instead of `https://smartparent.qubitrhythm.com`
2. **Namespace**: Uses `smartparent-test` instead of `smartparent`
3. **Docker Image**: Uses `gcr.io/smartparent/cloud-function-test:latest` instead of `gcr.io/smartparent/cloud-function:latest`
4. **Configuration**: Hard-coded to use production mode with test server URL
5. **Stripe Keys**: Uses test Stripe keys but production price IDs

## Deployment

To deploy the test-production environment to GCP:

1. Copy the entire `smartparent-k8s-test` directory to your GCP environment
2. Run the `deploy.sh` script:
   ```
   cd smartparent-k8s-test
   ./deploy.sh
   ```

## GCP Requirements

The following resources are needed in the GCP environment:

1. A Kubernetes cluster named `smartparent-k8s-test`
2. Permissions to create and manage Kubernetes resources
3. Access to Google Container Registry (GCR)
4. DNS configuration for `test.qubitrhythm.com` pointing to the Kubernetes ingress

## Testing with Chrome Extension

To test the test-production environment with the Chrome extension:

1. Make sure the Chrome extension is in production mode (not test mode)
2. The extension will use the production server URL (`https://smartparent.qubitrhythm.com`)
3. To test with the test-production server, you can temporarily modify the extension's server URL to point to `https://test.qubitrhythm.com`

## Migrating to Production

After testing in the test-production environment, you can migrate to the actual production environment by:

1. Verifying that all features work correctly in the test-production environment
2. Deploying the same code to the production environment with appropriate configuration changes
3. Updating the Chrome extension to use the production server URL

## Files Modified for Test-Production

The following files have been modified for the test-production environment:

- `cloud-function/config.js`: Modified to always use test server URL with production mode
- `k8s/cloud-function-deployment.yaml`: Updated namespace and container image
- `k8s/cloud-function-secrets.yaml`: Updated namespace and added test Stripe keys
- `k8s/cloud-function-service.yaml`: Updated namespace
- `k8s/postgres-deployment.yaml`: Updated namespace
- `k8s/postgres-service.yaml`: Updated namespace
- `k8s/postgres-pvc.yaml`: Updated namespace
- `k8s/postgres-secrets.yaml`: Created with test database credentials
- `k8s/db-init-job.yaml`: Updated namespace
- `k8s/db-init-configmap.yaml`: Updated namespace
- `deploy.sh`: Updated to use test-production environment settings
