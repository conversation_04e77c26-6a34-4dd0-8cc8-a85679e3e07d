<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warning - SmartParent</title>
    <style>
        /* CSS Variables for consistent styling - Matched with popup.css */
        :root {
            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Secondary Colors */
            --secondary-color: #5e35b1;
            --secondary-light: #7e57c2;
            --secondary-dark: #4527a0;
            --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

            /* Accent Colors */
            --accent-color: #f4b400;
            --accent-light: #ffc107;
            --accent-dark: #e69c00;
            --accent-gradient: linear-gradient(135deg, #f4b400 0%, #e69c00 100%);

            /* Status Colors */
            --success-color: #0f9d58;
            --success-light: #4caf50;
            --success-bg: rgba(15, 157, 88, 0.08);
            --error-color: #db4437;
            --error-light: #ef5350;
            --error-bg: rgba(219, 68, 55, 0.08);
            --warning-color: #f4b400;
            --info-color: #4285f4;

            /* Neutral Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --text-lighter: #80868b;
            --border-color: #dadce0;
            --border-light: #f1f3f4;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;
            --hover-bg: rgba(66, 133, 244, 0.08);
            --premium-bg: #e8f0fe;
            --dialog-bg: #fef7e0;
            --modal-bg: #e6f4ea;
            --subscribe-bg: #fce8e6;
            --warning-bg: #fef7e0;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --button-radius: 8px;
            --input-radius: 8px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            background-image:
                url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background-color: var(--container-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='44' height='12' viewBox='0 0 44 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 12v-2L0 0v10l4 2h16zm18 0l4-2V0L22 10v2h16zM20 0v8L4 0h16zm18 0L22 8V0h16z' fill='%23f4b400' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
            border: 1px solid var(--border-light);
        }

        .warning-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--warning-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .warning-icon::before {
            content: "⚠";
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .warning-box {
            background-color: var(--warning-bg);
            border-radius: var(--border-radius);
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid rgba(244, 180, 0, 0.2);
            box-shadow: var(--box-shadow);
        }

        .warning-box h2 {
            color: var(--warning-color);
            font-size: 2em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
            font-weight: 600;
        }

        .warning-box p {
            color: var(--text-color);
            font-size: 1.2em;
            line-height: 1.8;
            margin: 0;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .warning-box h2 {
                font-size: 1.6em;
            }

            .warning-box p {
                font-size: 1.1em;
            }

            .warning-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e1e1e1;
                --background-color: #1a1a1a;
                --container-bg: #2d2d2d;
                --warning-bg: #3F1D1D;
                --border-light: #3d3d3d;
            }

            .warning-box {
                border-color: rgba(244, 180, 0, 0.3);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-icon"></div>
        <h1>Access Warning</h1>
        <div class="warning-box">
            <h2>Website Blocked</h2>
            <p>This website has been identified as potentially inappropriate or unsafe based on your SmartParent settings.</p>
        </div>
    </div>
</body>
</html>
