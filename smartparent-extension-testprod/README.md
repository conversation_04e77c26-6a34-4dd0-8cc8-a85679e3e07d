# SmartParent Extension (Test-Production Version)

This directory contains a modified version of the SmartParent Chrome extension configured for the test-production environment. This version is designed to work with the test-production server at `https://test.qubitrhythm.com`.

## Overview

The test-production extension:
- Uses the test server URL (`https://test.qubitrhythm.com`) instead of the production server URL
- Has a modified name to distinguish it from the production version
- Has a different version number (2.1.1) to distinguish it from the production version (2.1.0)
- Includes permissions for both production and test-production server URLs

## Key Differences from Production

1. **Server URL**: Always uses `https://test.qubitrhythm.com` instead of `https://smartparent.qubitrhythm.com`
2. **Extension Name**: Uses "Smart Parental Control (Test-Prod)" instead of "Smart Parental Control"
3. **Version Number**: Uses "2.1.1" instead of "2.1.0"
4. **Permissions**: Includes permissions for both production and test-production server URLs
5. **Content Security Policy**: Includes both production and test-production server URLs

## Installation

To install the test-production extension:

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top-right corner
3. Click "Load unpacked" and select the `smartparent-extension-test` directory
4. The extension will be installed with the name "Smart Parental Control (Test-Prod)"

## Testing

When testing the test-production extension:

1. The extension will automatically connect to the test-production server at `https://test.qubitrhythm.com`
2. All API calls will be made to the test-production server
3. The extension will display "(Test-Prod)" in its name to distinguish it from the production version

## Files Modified for Test-Production

The following files have been modified for the test-production environment:

- `manifest.json`: Updated name, version, permissions, and content security policy
- `background.js`: Updated SERVER_URL to use the test-production server URL
- `subscribe.js`: Updated to always use the test-production server URL

## Migrating to Production

After testing in the test-production environment, you can migrate to the actual production environment by:

1. Verifying that all features work correctly in the test-production environment
2. Publishing the production version of the extension to the Chrome Web Store
3. Deploying the production server code to the production environment
