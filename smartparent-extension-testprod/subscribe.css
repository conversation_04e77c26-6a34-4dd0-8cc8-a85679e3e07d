/* CSS Variables for consistent styling - Matched with popup.css */
:root {
    /* Force Light Theme */
    color-scheme: light;

    /* Primary Colors */
    --primary-color: #4285f4;
    --primary-light: #5a95f5;
    --primary-dark: #3367d6;
    --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

    /* Secondary Colors */
    --secondary-color: #5e35b1;
    --secondary-light: #7e57c2;
    --secondary-dark: #4527a0;
    --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

    /* Accent Colors */
    --accent-color: #f4b400;
    --accent-light: #ffc107;
    --accent-dark: #e69c00;
    --accent-gradient: linear-gradient(135deg, #f4b400 0%, #e69c00 100%);

    /* Status Colors */
    --success-color: #0f9d58;
    --success-light: #4caf50;
    --success-bg: rgba(15, 157, 88, 0.08);
    --error-color: #db4437;
    --error-light: #ef5350;
    --error-bg: rgba(219, 68, 55, 0.08);
    --warning-color: #f4b400;
    --info-color: #4285f4;

    /* Neutral Colors */
    --text-color: #202124;
    --text-light: #5f6368;
    --text-lighter: #80868b;
    --border-color: #dadce0;
    --border-light: #f1f3f4;

    /* Background Colors */
    --background-color: #e8f0fe;
    --container-bg: #f8f9fa;
    --hover-bg: rgba(66, 133, 244, 0.08);
    --premium-bg: #e8f0fe;
    --premium-box-bg: #d0e1fc; /* Darker blue for inner boxes */
    --dialog-bg: #fef7e0;
    --modal-bg: #e6f4ea;
    --subscribe-bg: #fce8e6;

    /* UI Elements */
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
    --button-radius: 8px;
    --input-radius: 8px;
}

/* Disable dark mode completely */
@media (prefers-color-scheme: dark) {
    /* This media query is intentionally left empty to prevent dark mode */
}

body {
    font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
    margin: 0;
    padding: 0;
    background-color: white;
    color: var(--text-color);
    line-height: 1.6;
}

header {
    background: var(--primary-gradient);
    color: #fff;
    padding: 70px 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(66, 133, 244, 0.3);
    margin-bottom: 50px;
    position: relative;
}

/* Removed oblique lines under header */

header h1 {
    margin: 0;
    font-size: 3em;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-family: 'Arial', 'Helvetica Neue', sans-serif;
    color: white;
}

.features, .video-tutorial, .plans, .why-subscribe, .about {
    padding: 30px 30px;
    max-width: 1200px;
    margin: 0 auto 30px;
    background-color: var(--premium-bg);
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(66, 133, 244, 0.2);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 30px;
    border-radius: var(--border-radius);
    background-color: var(--premium-box-bg);
    transition: all 0.3s ease;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(66, 133, 244, 0.2);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(46,91,255,0.15);
    border-color: var(--primary-color);
}

.feature-icon {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.feature-item h3 {
    color: var(--primary-color);
    margin: 15px 0;
    font-size: 1.3em;
    font-weight: 600;
}

.feature-item p {
    color: var(--text-light);
    font-size: 1em;
    line-height: 1.6;
}

.plan-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 40px auto;
    max-width: 1200px;
}

.plan {
    background-color: var(--premium-box-bg);
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    border: 1px solid rgba(66, 133, 244, 0.2);
    width: 100%;
    max-width: 380px;
    text-align: center;
    padding: 40px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    box-shadow: var(--box-shadow);
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 700px;
}

.plan .plan-header {
    flex-shrink: 0;
}

.plan ul {
    flex-grow: 1;
    margin: 30px 0;
    list-style: none;
    padding: 0;
}

.plan button,
.plan input[type="email"] {
    margin-top: auto;
    width: calc(100% - 30px);
}

.plan.premium {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(66, 133, 244, 0.2);
}

.badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(46,91,255,0.2);
}

.plan li.included {
    color: var(--text-color);
}

.plan li.disabled {
    color: var(--text-light);
    text-decoration: line-through;
}

.plan li.disabled::before {
    content: "✗";
    color: var(--text-light);
}

.basic-button {
    width: calc(100% - 30px);
    padding: 16px 30px;
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--button-radius);
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 10px 0;
    font-family: 'Arial', 'Helvetica Neue', sans-serif;
}

.basic-button:hover {
    background: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
}

.plan.premium:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 28px rgba(66, 133, 244, 0.25);
}

.plan-header {
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid var(--border-color);
}

.subscription-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
}

.subscription-option {
    flex: 1;
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: var(--input-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.subscription-option.active {
    border-color: var(--primary-color);
    background-color: rgba(66, 133, 244, 0.08);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15);
}

.subscription-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.price {
    font-size: 2.5em;
    color: var(--primary-color);
    margin: 10px 0 5px;
    font-weight: 600;
}

.price span {
    font-size: 0.35em;
    color: var(--text-light);
    font-weight: normal;
}

.price-note {
    font-size: 0.85em;
    color: var(--text-light);
    margin: 0;
}

.plan h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 2em;
    font-weight: 600;
}

.plan li {
    padding: 12px 0;
    color: var(--text-color);
    font-size: 1.1em;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.plan li::before {
    content: "✓";
    color: var(--primary-color);
    margin-right: 10px;
    font-weight: bold;
}

.plan input[type="email"] {
    width: calc(100% - 30px);
    padding: 15px;
    margin: 20px 0;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1.1em;
    transition: all 0.3s ease;
    background-color: var(--container-bg);
    color: var(--text-color);
}

.plan input[type="email"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 4px 8px rgba(46,91,255,0.15);
}

.subscribe-button {
    width: calc(100% - 30px);
    padding: 16px 30px;
    background: var(--primary-gradient);
    color: #fff;
    border: none;
    border-radius: var(--button-radius);
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    margin: 10px 0;
    font-family: 'Arial', 'Helvetica Neue', sans-serif;
}

.subscribe-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4);
    filter: brightness(1.1);
}

/* Styles for the Coming Soon plan */
.plan.coming-soon {
    opacity: 0.7; /* Make it slightly faded */
    border-color: var(--text-light); /* Use a lighter border */
    transform: scale(1); /* Don't apply the enterprise scaling */
}

.plan.coming-soon .plan-header {
     border-bottom-color: var(--text-light); /* Match border */
}

.plan.coming-soon h3,
.plan.coming-soon .price {
    color: var(--text-light); /* Use lighter text color for header/price */
}

.plan.coming-soon li {
    color: var(--text-light); /* Lighter text for features */
    font-style: italic; /* Italicize future features */
    border-bottom: 1px dashed var(--border-color); /* Dashed line for separation */
}

.plan.coming-soon li::before {
    content: "•"; /* Use a simple bullet point */
    color: var(--text-light);
    margin-right: 10px;
    font-weight: normal;
}

.plan.coming-soon .badge {
    background: var(--text-light); /* Gray badge */
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Style for future features in coming-soon plan */
.plan.coming-soon li.future-feature {
    color: var(--text-light); /* Match other coming-soon text */
    font-style: italic;
    border-bottom: 1px dashed var(--border-color); /* Keep dashed line */
}

.plan.coming-soon li.future-feature::before {
    content: "•"; /* Use a simple bullet point */
    color: var(--text-light);
    margin-right: 10px;
    font-weight: normal;
}


.error {
    color: var(--error-color);
    background-color: var(--container-bg);
    padding: 12px;
    border-radius: 8px;
    margin: 20px 0;
    font-size: 0.9em;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--error-color);
}

footer {
    background: var(--primary-gradient);
    color: #fff;
    padding: 40px 0;
    text-align: center;
    margin-top: 60px;
    box-shadow: 0 -4px 12px rgba(0,0,0,0.1);
}

footer p {
    margin: 0;
    opacity: 0.9;
}

footer nav {
    margin-top: 20px;
}

footer nav a {
    color: #fff;
    margin: 0 15px;
    text-decoration: none;
    font-weight: 500;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

footer nav a:hover {
    opacity: 1;
    text-decoration: underline;
}

h2 {
    color: var(--primary-color);
    text-align: center;
    font-size: 2.2em;
    margin-bottom: 40px;
    font-weight: 700;
    position: relative;
    padding-bottom: 15px;
}

h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.why-subscribe p, .about p {
    font-size: 1.1em;
    color: var(--text-light);
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.video-container { /* Renamed from video-placeholder */
    display: flex; /* Enable Flexbox */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically (optional, but good practice) */
    margin: 20px 0; /* Add some space around the container */
    background-color: var(--premium-box-bg);
    border: 1px solid rgba(66, 133, 244, 0.2);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px; /* Add padding inside the container */
    overflow: hidden; /* Ensure iframe doesn't overflow */
}

.video-container iframe {
    max-width: 100%; /* Make iframe responsive */
    border-radius: var(--border-radius); /* Optional: round corners */
    box-shadow: var(--box-shadow); /* Optional: add shadow to iframe */
}

@media screen and (max-width: 768px) {
    header h1 {
        font-size: 2em;
    }

    .plan-container {
        padding: 0 20px;
    }

    .plan {
        transform: scale(1) !important;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }
}
