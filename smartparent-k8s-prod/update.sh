#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in update script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details for production environment
PROJECT_ID="smartparent"
NAMESPACE="smartparent"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Build and push cloud function image for production
echo "Building cloud function Docker image for production..."
docker build -t gcr.io/$PROJECT_ID/cloud-function:latest -f cloud-function/Dockerfile cloud-function

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing cloud function image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/cloud-function:latest

echo "Updating cloud function deployment..."
kubectl apply -f k8s/cloud-function-secrets.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-deployment.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-service.yaml -n $NAMESPACE

# Alternative approach using kubectl set image
# echo "Updating cloud function image..."
# kubectl set image deployment/cloud-function cloud-function=gcr.io/$PROJECT_ID/cloud-function:latest -n $NAMESPACE

# Check rollout status
echo "Checking rollout status..."
kubectl rollout status deployment/cloud-function -n $NAMESPACE

echo "Verifying deployments..."
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE
kubectl get deployments -n $NAMESPACE

echo "Update completed successfully!"
echo "The server is accessible at https://smartparent.qubitrhythm.com"
