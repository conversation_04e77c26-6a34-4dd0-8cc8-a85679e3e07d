apiVersion: v1
kind: Secret
metadata:
  name: cloud-function-secrets
  namespace: smartparent
type: Opaque
data:
  PLAN_PRICE_IDS_STANDARD: cHJpY2VfMVFxZ3FWQVI3VmxVSXJFeDB0NGpKM25O # Original price ID ($5.99 monthly)
  PLAN_PRICE_IDS_MONTHLY: cHJpY2VfMVJQOThvQVI3VmxVSXJFeGF0cXJ4QUVF  # price_1RP98oAR7VlUIrExatqrxAEE ($2.99 monthly)
  PLAN_PRICE_IDS_ANNUAL: cHJpY2VfMVJQOTlQQVI3VmxVSXJFeFFpUEljZ1ps   # price_1RP99PAR7VlUIrExQiPIcgZl ($29.99 annually)
  STRIPE_SECRET_KEY: ************************************************************************************************************************************************
  STRIPE_WEBHOOK_SECRET: d2hzZWNfSkJDaXZJYUZKYmZRanBmdTFTS0IzVEUyVW9ZeXlOSnA=
  #DEEPSEEK_API_URL: aHR0cHM6Ly9hcGkuZGVlcHNlZWsuY29t
  #DEEPSEEK_API_KEY: ************************************************
  GROK_MODEL: Z3Jvay0zLW1pbmktZmFzdC1iZXRh
  GROK_API_URL: aHR0cHM6Ly9hcGkueC5haS92MS9jaGF0L2NvbXBsZXRpb25z
  GROK_API_KEY: ****************************************************************************************************************
  FROM_EMAIL: ****************************
  EMAIL_PASSWORD: dHZjc3lsbG90a215eXh4dw==
  SMTP_HOST: c210cC5nbWFpbC5jb20=
  SMTP_PORT: NDY1
  SMTP_SECURE: dHJ1ZQ==
  DATABASE_URL: ****************************************************************************
  FIREBASE_PROJECT_ID: cmNnLWludGVybmFs
  SERVER_URL: aHR0cHM6Ly9zbWFydHBhcmVudC5xdWJpdHJoeXRobS5jb20=
  DB_SSL: "ZmFsc2U="  # echo -n "false" | base64
