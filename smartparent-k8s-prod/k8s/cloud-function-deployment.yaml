apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-function
  namespace: smartparent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cloud-function
  template:
    metadata:
      labels:
        app: cloud-function
    spec:
      containers:
      - name: cloud-function
        image: gcr.io/smartparent/cloud-function:latest
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /check
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /check
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        env:
        - name: SERVER_URL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: SERVER_URL
        - name: TEST_SERVER_URL
          value: "https://test.qubitrhythm.com"
        - name: NODE_ENV
          value: "production"
        - name: TEST_MODE
          value: "false"  # Set to "true" for test mode, "false" for production
        - name: PORT
          value: "8080"
        - name: CHECK_ENDPOINT
          value: "/check"
        - name: TIMEOUT
          value: "5000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: DATABASE_URL
        - name: DB_SSL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: DB_SSL
        - name: PGHOST
          value: "postgres.smartparent.svc.cluster.local"
        - name: PGPORT
          value: "5432"
        - name: PGDATABASE
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_DB
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_PASSWORD
        - name: PLAN_PRICE_IDS_STANDARD
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: PLAN_PRICE_IDS_STANDARD
        - name: PLAN_PRICE_IDS_MONTHLY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: PLAN_PRICE_IDS_MONTHLY
        - name: PLAN_PRICE_IDS_ANNUAL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: PLAN_PRICE_IDS_ANNUAL
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: STRIPE_SECRET_KEY
        - name: GROK_API_URL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: GROK_API_URL
        - name: GROK_API_KEY
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: GROK_API_KEY
        - name: GROK_MODEL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: GROK_MODEL
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: STRIPE_WEBHOOK_SECRET
        - name: FROM_EMAIL
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: FROM_EMAIL
        - name: EMAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: EMAIL_PASSWORD
        - name: SMTP_HOST
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: SMTP_HOST
        - name: SMTP_PORT
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: SMTP_PORT
        - name: SMTP_SECURE
          valueFrom:
            secretKeyRef:
              name: cloud-function-secrets
              key: SMTP_SECURE
