apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - selector:
        dnsZones:
          - "qubitrhythm.com"
      dns01:
        cloudDNS:
          project: smartparent
          hostedZoneName: smartparent-zone
          serviceAccountSecretRef:
            name: clouddns-service-account
            key: credentials.json
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: smartparent-tls
  namespace: smartparent
spec:
  secretName: smartparent-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - "*.smartparent.qubitrhythm.com"  # Wildcard certificate for all subdomains
  - smartparent.qubitrhythm.com      # Base domain
  usages:
    - server auth
    - client auth
