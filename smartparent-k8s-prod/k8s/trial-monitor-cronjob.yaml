apiVersion: batch/v1
kind: CronJob
metadata:
  name: smartparent-trial-monitor
  namespace: smartparent
spec:
  # Schedule to run daily at 3:00 AM UTC
  # Adjust as needed: https://crontab.guru/
  schedule: "0 3 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trial-monitor
            # IMPORTANT: Replace YOUR_GCP_PROJECT_ID with your actual GCP project ID
            image: gcr.io/smartparent/smartparent-trial-monitor:latest
            imagePullPolicy: Always # Ensure the latest image is pulled
            env:
            # Database Credentials from postgres-secrets
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_USER
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: POSTGRES_DB
            - name: POSTGRES_HOST
              value: "postgres.smartparent.svc.cluster.local"
            - name: POSTGRES_PORT
              value: "5432"

            # Test mode configuration
            - name: TEST_MODE
              value: "false"  # Set to "true" for test mode, "false" for production

            # Email and Server URL Credentials from cloud-function-secrets
            - name: FROM_EMAIL
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: FROM_EMAIL
            - name: EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: EMAIL_PASSWORD
            - name: SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: SERVER_URL
            - name: SMTP_HOST
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: SMTP_HOST
            - name: SMTP_PORT
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: SMTP_PORT
            - name: SMTP_SECURE
              valueFrom:
                secretKeyRef:
                  name: cloud-function-secrets
                  key: SMTP_SECURE

          restartPolicy: OnFailure # Restart the job container if it fails
      backoffLimit: 1 # Number of retries before marking job as failed
  concurrencyPolicy: Forbid # Prevents multiple jobs from running concurrently if one takes too long
  successfulJobsHistoryLimit: 3 # Keep history of last 3 successful jobs
  failedJobsHistoryLimit: 1 # Keep history of last 1 failed job
