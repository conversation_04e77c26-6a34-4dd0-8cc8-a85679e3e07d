<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Success - SmartParent</title>
    <style>
        :root {
            --primary-color: #2E5BFF;
            --success-color: #34D399;
            --secondary-color: #2c3e50;
            --text-color: #333333;
            --background-color: #f8f9fa;
            --container-bg: #ffffff;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background: var(--container-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--success-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
        }

        .success-icon::before {
            content: "✓";
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: var(--text-color);
            line-height: 1.8;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #1a3ccc;
            text-decoration: underline;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            p {
                font-size: 1em;
            }

            .success-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e1e1e1;
                --background-color: #1a1a1a;
                --container-bg: #2d2d2d;
                --success-color: #059669;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon"></div>
        <h1>Subscription Successful!</h1>
        <p>Thank you for subscribing to SmartParent. Your subscription has been successfully processed, and your account has been activated with full access to all features.</p>
        <p>You can now close this window and continue using SmartParent with your full subscription benefits.</p>
        <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>
</body>
</html>
