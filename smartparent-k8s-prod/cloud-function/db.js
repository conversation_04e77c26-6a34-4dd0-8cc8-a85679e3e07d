const { Pool } = require('pg');
const config = require('./config');
require('dotenv').config();

// Parse database URL if provided
let dbConfig = {};
if (config.databaseUrl) {
  const url = new URL(config.databaseUrl);
  dbConfig = {
    user: url.username || process.env.PGUSER,
    password: url.password || process.env.PGPASSWORD,
    host: url.hostname || process.env.PGHOST,
    port: url.port || process.env.PGPORT || 5432,
    database: url.pathname.substring(1) || process.env.PGDATABASE,
    ssl: config.dbSsl ? { rejectUnauthorized: false } : false
  };
} else {
  dbConfig = {
    user: process.env.PGUSER,
    host: process.env.PGHOST,
    database: process.env.PGDATABASE,
    password: process.env.PGPASSWORD,
    port: process.env.PGPORT || 5432,
    ssl: config.dbSsl ? { rejectUnauthorized: false } : false
  };
}

console.log(`Connecting to database in ${config.isTestMode ? 'TEST' : 'PRODUCTION'} mode`);
console.log(`Database host: ${dbConfig.host}, database: ${dbConfig.database}`);

const pool = new Pool(dbConfig);

// Database configuration
const MAX_RETRIES = 5;          // Maximum number of connection retry attempts
const INITIAL_DELAY_MS = 2000;  // Initial delay before first retry, doubles with each attempt

/**
 * Initialize database connection and create installations table
 * Uses exponential backoff for connection retries
 * @returns {Promise<void>}
 * @throws {Error} If database initialization fails after max retries
 */
async function initializeDatabase() {
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      // Test connection first
      await pool.query('SELECT 1');

      // Create installations table (matches db-init.sql for consistency)
      // Ensure table schema matches db-init.sql
      await pool.query(`
        CREATE TABLE IF NOT EXISTS installations (
            ip_hash TEXT PRIMARY KEY,
            email TEXT NULL,
            status TEXT NOT NULL,
            install_timestamp TIMESTAMP DEFAULT NOW(),
            activation_timestamp TIMESTAMP NULL,
            trial_used BOOLEAN DEFAULT FALSE,
            trial_reminder_sent_at TIMESTAMP NULL
        );
      `);

      // Create survey_responses table
      await pool.query(`
        CREATE TABLE IF NOT EXISTS survey_responses (
            response_id SERIAL PRIMARY KEY,
            email TEXT NULL,
            ip_hash TEXT NULL,
            reason TEXT NOT NULL,
            details TEXT NULL,
            calculated_usage_duration_days INTEGER NULL,
            submission_timestamp TIMESTAMP DEFAULT NOW()
        );
      `);

      // Create indexes (moved outside CREATE TABLE block)
      await pool.query('CREATE INDEX IF NOT EXISTS idx_installations_ip_hash ON installations(ip_hash)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_installations_email ON installations(email)'); // Add email index if missing
      await pool.query('CREATE INDEX IF NOT EXISTS idx_installations_status ON installations(status)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_survey_responses_ip_hash ON survey_responses(ip_hash)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_survey_responses_email ON survey_responses(email)');

      console.log('Database schema verified/initialized successfully');
      return;
    } catch (err) {
      if (err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') {
        const delay = INITIAL_DELAY_MS * Math.pow(2, retries);
        console.error(`Connection failed (attempt ${retries + 1}/${MAX_RETRIES}), retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
      } else {
        console.error('Database initialization error:', err);
        throw err;
      }
    }
  }
  throw new Error(`Failed to initialize database after ${MAX_RETRIES} attempts`);
}

// --- Installation Management Functions ---

/**
 * Get installation details by IP hash.
 * @param {string} ip_hash - The hashed IP address.
 * @returns {Promise<object|null>} Installation record or null if not found.
 */
async function getInstallation(ip_hash) {
  try {
    const result = await pool.query(
      'SELECT ip_hash, email, status, install_timestamp FROM installations WHERE ip_hash = $1',
      [ip_hash]
    );
    return result.rows[0] || null;
  } catch (err) {
    console.error('Error getting installation:', err);
    throw err; // Re-throw to be handled by caller
  }
}

/**
 * Add a new installation record.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} initialStatus - The initial status (e.g., 'TrialPendingEmail').
 * @returns {Promise<object>} The newly created installation record.
 */
async function addInstallation(ip_hash, initialStatus) {
  try {
    const result = await pool.query(
      'INSERT INTO installations (ip_hash, status) VALUES ($1, $2) RETURNING ip_hash, email, status, install_timestamp',
      [ip_hash, initialStatus]
    );
    console.log(`Added installation for ${ip_hash} with status ${initialStatus}`);
    return result.rows[0];
  } catch (err) {
    // Handle potential race condition if insert happens concurrently
    if (err.code === '23505') { // unique_violation
      console.warn(`Installation already exists for ${ip_hash}, fetching existing.`);
      return getInstallation(ip_hash);
    }
    console.error('Error adding installation:', err);
    throw err;
  }
}

/**
 * Sets the activation timestamp for an installation.
 * Call this when a user successfully activates with their email.
 * @param {string} ip_hash - The hashed IP address.
 * @returns {Promise<object|null>} Updated installation record or null if not found/already set.
 */
async function updateActivationTimestamp(ip_hash) {
  try {
    // Only set the timestamp if it's currently NULL
    const result = await pool.query(
      `UPDATE installations
       SET activation_timestamp = NOW()
       WHERE ip_hash = $1 AND activation_timestamp IS NULL
       RETURNING ip_hash, email, status, install_timestamp, activation_timestamp`,
      [ip_hash]
    );
    if (result.rowCount > 0) {
      console.log(`Set activation timestamp for ${ip_hash}`);
      return result.rows[0];
    }
    console.log(`Activation timestamp already set or installation not found for ${ip_hash}`);
    return null; // Indicate not updated (already set or not found)
  } catch (err) {
    console.error('Error setting activation timestamp:', err);
    throw err;
  }
}


/**
 * Update the email for an installation.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} email - The email address to set.
 * @returns {Promise<object|null>} Updated installation record or null if not found.
 */
async function updateInstallationEmail(ip_hash, email) {
  try {
    const result = await pool.query(
      'UPDATE installations SET email = $1 WHERE ip_hash = $2 RETURNING ip_hash, email, status, install_timestamp',
      [email, ip_hash]
    );
    if (result.rowCount > 0) {
        console.log(`Updated email for ${ip_hash}`);
        return result.rows[0];
    }
    return null; // Indicate record not found or not updated
  } catch (err) {
    console.error('Error updating installation email:', err);
    throw err;
  }
}

/**
 * Update the status for an installation.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} status - The new status to set.
 * @returns {Promise<object|null>} Updated installation record or null if not found.
 */
async function updateInstallationStatus(ip_hash, status) {
    try {
      const result = await pool.query(
        'UPDATE installations SET status = $1 WHERE ip_hash = $2 RETURNING ip_hash, email, status, install_timestamp',
        [status, ip_hash]
      );
      if (result.rowCount > 0) {
          console.log(`Updated status for ${ip_hash} to ${status}`);
          return result.rows[0];
      }
      return null; // Indicate record not found or not updated
    } catch (err) {
      console.error('Error updating installation status:', err);
      throw err;
  }
}

/**
 * Get installation details by email address.
 * Can return multiple installations if the same email is associated with different IPs.
 * @param {string} email - The email address.
 * @returns {Promise<Array<object>>} Array of installation records or empty array if none found.
 */
async function getInstallationsByEmail(email) {
  if (!email) {
    console.warn('getInstallationsByEmail called with null or empty email.');
    return [];
  }
  try {
    const result = await pool.query(
      'SELECT ip_hash, email, status, install_timestamp FROM installations WHERE email = $1',
      [email]
    );
    return result.rows || [];
  } catch (err) {
    console.error('Error getting installations by email:', err);
    throw err; // Re-throw to be handled by caller
  }
}


/**
 * Starts the premium trial for an installation.
 * Sets status to TrialActive, trial_used to TRUE, and activation_timestamp to NOW().
 * @param {string} ip_hash - The hashed IP address.
 * @returns {Promise<object|null>} Updated installation record or null if not found.
 */
async function startPremiumTrial(ip_hash) {
    try {
      const result = await pool.query(
        `UPDATE installations
         SET status = 'TrialActive',
             trial_used = TRUE,
             activation_timestamp = NOW()
         WHERE ip_hash = $1
         RETURNING ip_hash, email, status, install_timestamp, activation_timestamp, trial_used`,
        [ip_hash]
      );
      if (result.rowCount > 0) {
          console.log(`Started Premium trial for ${ip_hash}`);
          return result.rows[0];
      }
      console.log(`Could not start trial for ${ip_hash} (not found?)`);
      return null; // Indicate record not found or not updated
    } catch (err) {
      console.error('Error starting premium trial:', err);
      throw err;
  }
}


// --- Survey Response Function ---

/**
 * Adds a survey response to the database.
 * @param {string|null} email - User's email if available.
 * @param {string|null} ip_hash - User's IP hash if available.
 * @param {string} reason - The selected uninstall reason.
 * @param {string|null} details - Optional free-text details.
 * @param {number|null} duration_days - Calculated usage duration in days.
 * @returns {Promise<object>} The newly created survey response record.
 */
async function addSurveyResponse(email, ip_hash, reason, details, duration_days) {
  try {
    const result = await pool.query(
      `INSERT INTO survey_responses (email, ip_hash, reason, details, calculated_usage_duration_days)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING response_id, email, ip_hash, reason, details, calculated_usage_duration_days, submission_timestamp`,
      [email, ip_hash, reason, details, duration_days]
    );
    console.log(`Added survey response (ID: ${result.rows[0].response_id})`);
    return result.rows[0];
  } catch (err) {
    console.error('Error adding survey response:', err);
    throw err;
  }
}


module.exports = {
  pool,
  initializeDatabase,
  getInstallation,
  addInstallation,
  updateActivationTimestamp, // Export new function
  updateInstallationEmail,
  updateInstallationStatus,
  getInstallationsByEmail,
  addSurveyResponse, // Export new function
  startPremiumTrial // Export new function
};
