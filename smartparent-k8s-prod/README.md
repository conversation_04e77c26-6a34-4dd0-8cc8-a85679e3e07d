# SmartParent Production Environment

This directory contains the production version of the SmartParent server code configured for the production environment. This environment is designed to serve real users with the production server configuration.

## Overview

The production environment:
- Uses the production server URL (`https://smartparent.qubitrhythm.com`)
- Operates in production mode (not test mode)
- Uses production price IDs for Stripe subscriptions
- Uses production Stripe keys for payment processing
- Runs in the production Kubernetes namespace (`smartparent`)

## Key Differences from Test-Production

1. **Server URL**: Uses `https://smartparent.qubitrhythm.com` instead of `https://test.qubitrhythm.com`
2. **Namespace**: Uses `smartparent` instead of `smartparent-test`
3. **Docker Image**: Uses `gcr.io/smartparent/cloud-function:latest` instead of `gcr.io/smartparent/cloud-function-test:latest`
4. **Configuration**: Uses production mode with production server URL
5. **Stripe Keys**: Uses production Stripe keys with production price IDs
6. **Price IDs**: Uses production price IDs:
   - Monthly ($2.99): `price_1RP98oAR7VlUIrExatqrxAEE`
   - Annual ($29.99): `price_1RP99PAR7VlUIrExQiPIcgZl`

## Deployment

To deploy the production environment to GCP:

1. Copy the entire `smartparent-k8s-prod` directory to your GCP environment
2. Run the `deploy.sh` script:
   ```
   cd smartparent-k8s-prod
   ./deploy.sh
   ```

## GCP Requirements

The following resources are needed in the GCP environment:

1. A Kubernetes cluster named `smartparent-k8s`
2. Permissions to create and manage Kubernetes resources
3. Access to Google Container Registry (GCR)
4. DNS configuration for `smartparent.qubitrhythm.com` pointing to the Kubernetes ingress

## Testing with Chrome Extension

To test the production environment with the Chrome extension:

1. Make sure the Chrome extension is in production mode (not test mode)
2. The extension will use the production server URL (`https://smartparent.qubitrhythm.com`)
3. The extension will use production price IDs for Stripe subscriptions

## Backward Compatibility

The production server is designed to be backward compatible with existing users of the old version extension (v2.1.0). The server will continue to serve these users without any issues.

## Files Modified for Production

The following files have been modified for the production environment:

- `cloud-function/config.js`: Updated to use production mode with production server URL and price IDs
- `k8s/cloud-function-deployment.yaml`: Updated namespace and container image
- `k8s/cloud-function-secrets.yaml`: Updated namespace and added production Stripe keys and price IDs
- `k8s/cloud-function-service.yaml`: Updated namespace
- `k8s/postgres-deployment.yaml`: Updated namespace
- `k8s/postgres-service.yaml`: Updated namespace
- `k8s/postgres-pvc.yaml`: Updated namespace
- `k8s/postgres-secrets.yaml`: Updated with production database credentials
- `k8s/db-init-job.yaml`: Updated namespace
- `k8s/db-init-configmap.yaml`: Updated namespace
- `deploy.sh`: Updated to use production environment settings
