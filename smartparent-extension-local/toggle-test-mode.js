/**
 * <PERSON><PERSON><PERSON> to toggle test mode for the Chrome extension
 * 
 * This script updates the necessary files in the Chrome extension to switch between
 * test mode and production mode.
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
let enableTestMode = false;
let showHelp = false;

for (const arg of args) {
  if (arg === '--enable') {
    enableTestMode = true;
  } else if (arg === '--disable') {
    enableTestMode = false;
  } else if (arg === '--help' || arg === '-h') {
    showHelp = true;
  } else {
    console.error(`Unknown option: ${arg}`);
    showHelp = true;
  }
}

if (showHelp) {
  console.log('Usage: node toggle-test-mode.js [--enable|--disable]');
  console.log('');
  console.log('Options:');
  console.log('  --enable    Enable test mode');
  console.log('  --disable   Disable test mode (default)');
  console.log('  --help, -h  Show this help message');
  process.exit(0);
}

// Configuration
const testServerUrl = 'http://localhost:32410'; // Local test server URL
const prodServerUrl = 'https://smartparent.qubitrhythm.com'; // Production server URL

const filesToUpdate = [
  {
    path: 'background.js',
    testModeUpdates: [
      {
        search: /const SERVER_URL = ['"]https:\/\/smartparent\.qubitrhythm\.com['"];/,
        replace: `const SERVER_URL = '${testServerUrl}'; // Test mode`
      },
      {
        search: /const uninstallUrl = ['"]https:\/\/smartparent\.qubitrhythm\.com\/staticHosting\/uninstall_survey\.html['"];/,
        replace: `const uninstallUrl = '${testServerUrl}/staticHosting/uninstall_survey.html'; // Test mode`
      }
    ],
    prodModeUpdates: [
      {
        search: /const SERVER_URL = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const SERVER_URL = '${prodServerUrl}';`
      },
      {
        search: /const uninstallUrl = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const uninstallUrl = '${prodServerUrl}/staticHosting/uninstall_survey.html';`
      }
    ]
  },
  {
    path: 'popup/popup.js',
    testModeUpdates: [
      {
        search: /const SERVER_URL = ['"]https:\/\/smartparent\.qubitrhythm\.com['"];/,
        replace: `const SERVER_URL = '${testServerUrl}'; // Test mode`
      }
    ],
    prodModeUpdates: [
      {
        search: /const SERVER_URL = ['"][^'"]+['"];.*\/\/ Test mode/,
        replace: `const SERVER_URL = '${prodServerUrl}';`
      }
    ]
  },
  {
    path: 'subscribe.js',
    testModeUpdates: [
      {
        search: /const isTestMode = window\.location\.hostname\.includes\('test'\) \|\|/,
        replace: `const isTestMode = true || window.location.hostname.includes('test') ||`
      }
    ],
    prodModeUpdates: [
      {
        search: /const isTestMode = true \|\| window\.location\.hostname\.includes\('test'\) \|\|/,
        replace: `const isTestMode = window.location.hostname.includes('test') ||`
      }
    ]
  }
];

// Function to update files
function updateFile(filePath, updates) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    for (const update of updates) {
      if (update.search.test(content)) {
        content = content.replace(update.search, update.replace);
        updated = true;
      }
    }
    
    if (updated) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated ${filePath}`);
    } else {
      console.log(`No changes needed in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error.message);
  }
}

// Main function
function main() {
  console.log(`${enableTestMode ? 'Enabling' : 'Disabling'} test mode...`);
  
  for (const file of filesToUpdate) {
    const filePath = path.resolve(__dirname, file.path);
    const updates = enableTestMode ? file.testModeUpdates : file.prodModeUpdates;
    
    updateFile(filePath, updates);
  }
  
  // Create or update a test mode indicator file
  const testModeIndicatorPath = path.resolve(__dirname, '.test-mode');
  if (enableTestMode) {
    fs.writeFileSync(testModeIndicatorPath, 'true');
    console.log('Created test mode indicator file');
  } else {
    if (fs.existsSync(testModeIndicatorPath)) {
      fs.unlinkSync(testModeIndicatorPath);
      console.log('Removed test mode indicator file');
    }
  }
  
  console.log(`\nTest mode has been ${enableTestMode ? 'ENABLED' : 'DISABLED'}.`);
  console.log(`The extension is now using ${enableTestMode ? 'test' : 'production'} configuration.`);
  console.log('\nNote: You need to reload the extension in Chrome for changes to take effect.');
  console.log('You can do this by going to chrome://extensions and clicking the refresh icon on the extension.');
}

main();
