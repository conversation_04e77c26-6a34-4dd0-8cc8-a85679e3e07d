<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <title>Terms of Service - SmartParent</title>
    <style>
        :root {
            /* Force Light Theme */
            color-scheme: light;

            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Text Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --secondary-color: #5f6368;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --border-light: #f1f3f4;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            background-image:
                url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background-color: var(--container-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            border: 1px solid var(--border-light);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eef2f7;
        }

        .last-updated {
            color: var(--secondary-color);
            font-size: 0.9em;
            margin-top: 10px;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        h2 {
            color: var(--secondary-color);
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 15px;
            font-weight: 600;
            padding-top: 20px;
            border-top: 1px solid #eef2f7;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: var(--text-color);
            line-height: 1.8;
        }

        ul, ol {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #1a3ccc;
            text-decoration: underline;
        }

        .section {
            margin-bottom: 30px;
            padding-bottom: 20px;
        }

        .important {
            background-color: var(--background-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            border: 1px solid rgba(66, 133, 244, 0.2);
            box-shadow: var(--box-shadow);
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 20px;
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.3em;
            }

            p, li {
                font-size: 1em;
            }
        }

        /* Disable dark mode completely */
        @media (prefers-color-scheme: dark) {
            /* This media query is intentionally left empty to prevent dark mode */
        }
    </style>
    <script>
        // Force light mode
        document.documentElement.setAttribute('data-color-scheme', 'light');
    </script>
</head>
<body style="background-color: #e8f0fe !important;">
    <div class="container">
        <div class="header">
            <h1>Terms of Service</h1>
            <div class="last-updated">Last Updated: January 29, 2025</div>
        </div>

        <div class="important">
            <h2>Agreement to Terms</h2>
            <p>By installing and using the SmartParent browser extension, you agree to these Terms of Service. If you do not agree to these terms, please uninstall the extension immediately.</p>
        </div>

        <div class="section">
            <h2>1. Service Description</h2>
            <p>SmartParent is a parental control browser extension that provides:</p>
            <ul>
                <li>Website filtering and monitoring</li>
                <li>Activity reporting and notifications</li>
                <li>Time management features</li>
                <li>Safe browsing protection</li>
            </ul>
        </div>

        <div class="section">
            <h2>2. User Accounts</h2>
            <p>To use SmartParent, you must:</p>
            <ul>
                <li>Create and maintain an accurate account</li>
                <li>Protect your account credentials</li>
                <li>Promptly notify us of any security breaches</li>
                <li>Be at least 18 years old to create a parent account</li>
            </ul>
        </div>

        <div class="section">
            <h2>3. Subscription Terms</h2>
            <p>SmartParent operates on a subscription basis:</p>
            <ul>
                <li>Subscriptions are billed according to your chosen plan</li>
                <li>Automatic renewal occurs unless cancelled</li>
                <li>Refunds are provided per our refund policy</li>
                <li>Price changes will be notified in advance</li>
            </ul>
        </div>

        <div class="section">
            <h2>4. Acceptable Use</h2>
            <p>You agree not to:</p>
            <ul>
                <li>Circumvent or disable the extension</li>
                <li>Use the service for illegal purposes</li>
                <li>Share accounts between households</li>
                <li>Reverse engineer the extension</li>
            </ul>
        </div>

        <div class="section">
            <h2>5. Privacy and Data</h2>
            <p>Your privacy is important to us:</p>
            <ul>
                <li>We collect data as described in our Privacy Policy</li>
                <li>You consent to monitoring activities for children under your care</li>
                <li>Data is processed securely and confidentially</li>
            </ul>
        </div>

        <div class="section">
            <h2>6. Modifications</h2>
            <p>SmartParent reserves the right to:</p>
            <ul>
                <li>Modify or discontinue any feature</li>
                <li>Update these terms with notice</li>
                <li>Change subscription pricing</li>
            </ul>
        </div>

        <div class="section">
            <h2>7. Termination</h2>
            <p>We may suspend or terminate your access:</p>
            <ul>
                <li>For violation of these terms</li>
                <li>For fraudulent activity</li>
                <li>Upon account closure request</li>
            </ul>
        </div>

        <div class="section">
            <h2>8. Support</h2>
            <p>For support or questions about these terms, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>
