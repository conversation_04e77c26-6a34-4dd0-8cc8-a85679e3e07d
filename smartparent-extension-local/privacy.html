<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <title>Privacy Policy - SmartParent</title>
    <style>
        :root {
            /* Force Light Theme */
            color-scheme: light;

            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Text Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --secondary-color: #5f6368;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --border-light: #f1f3f4;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            background-image:
                url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 40px auto;
            padding: 40px;
            background-color: var(--container-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            border: 1px solid var(--border-light);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eef2f7;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        h2 {
            color: var(--secondary-color);
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: var(--text-color);
            line-height: 1.8;
        }

        ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #1a3ccc;
            text-decoration: underline;
        }

        .section {
            margin-bottom: 30px;
            padding-bottom: 20px;
        }

        .important-notice {
            background-color: var(--background-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            border: 1px solid rgba(66, 133, 244, 0.2);
            box-shadow: var(--box-shadow);
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 20px auto;
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.3em;
            }

            p, li {
                font-size: 1em;
            }
        }

        /* Disable dark mode completely */
        @media (prefers-color-scheme: dark) {
            /* This media query is intentionally left empty to prevent dark mode */
        }
    </style>
    <script>
        // Force light mode
        document.documentElement.setAttribute('data-color-scheme', 'light');
    </script>
</head>
<body style="background-color: #e8f0fe !important;">
    <div class="container">
        <div class="header">
            <h1>Privacy Policy</h1>
            <p>Last Updated: March 16, 2025</p>
        </div>

        <div class="important-notice">
            <h2>Your Privacy Matters</h2>
            <p>We are committed to protecting your privacy and ensuring the security of your personal information. This policy explains how we collect, use, and safeguard your data when you use our browser extension and related services.</p>
        </div>

        <div class="section">
            <h2>Browser Extension Privacy</h2>
            <p>Our Chrome extension requires certain permissions to function effectively:</p>
            <ul>
                <li>Access to active tabs for real-time monitoring and safety analysis</li>
                <li>Browsing history access for daily activity reports</li>
                <li>Storage permissions for saving local settings</li>
            </ul>
            <p>We only collect and process the minimum data necessary for the extension to function properly.</p>
        </div>

        <div class="section">
            <h2>Information Collection and Use</h2>
            <p>SmartParent collects and processes the following information:</p>
            <ul>
                <li>Email addresses for account management and communication</li>
                <li>Current day's browsing activity for daily reports (when enabled)</li>
                <li>Real-time URL analysis for safety monitoring</li>
                <li>Payment information for subscription management (processed securely through Stripe)</li>
                <li>Extension settings and preferences</li>
                <li>Technical information such as browser version and operating system</li>
            </ul>
        </div>

        <div class="section">
            <h2>Data Retention</h2>
            <p>We follow a minimal data retention policy:</p>
            <ul>
                <li>Browsing History: Only processed for same-day email reports, not stored long-term</li>
                <li>URL Safety Analysis: Real-time processing only, no historical storage</li>
                <li>Account Information: Retained while account is active plus 30 days after deletion</li>
                <li>Payment Information: As required by financial regulations through our payment processor</li>
                <li>Technical Logs: Standard server logs as per hosting provider policies</li>
            </ul>
            <p>Data is securely deleted when no longer needed for the service operation.</p>
        </div>

        <div class="section">
            <h2>Cookie Policy</h2>
            <p>We use cookies and similar technologies to:</p>
            <ul>
                <li>Maintain your session and authentication status</li>
                <li>Remember your preferences and settings</li>
                <li>Analyze usage patterns to improve our service</li>
            </ul>
            <p>You can manage cookie preferences through your browser settings, but disabling certain cookies may limit functionality.</p>
        </div>

        <div class="section">
            <h2>Data Security</h2>
            <p>We implement industry-standard security measures:</p>
            <ul>
                <li>Data encryption using TLS 1.3 for all transmissions</li>
                <li>Secure storage with regular security audits</li>
                <li>Access controls and authentication mechanisms</li>
                <li>Regular vulnerability assessments and penetration testing</li>
            </ul>
        </div>

        <div class="section">
            <h2>Your Privacy Rights</h2>
            <p>Under GDPR and CCPA, you have the right to:</p>
            <ul>
                <li>Access your personal data</li>
                <li>Request correction of inaccurate data</li>
                <li>Request deletion of your data (right to be forgotten)</li>
                <li>Export your data (data portability)</li>
                <li>Restrict or object to processing</li>
                <li>Withdraw consent at any time</li>
            </ul>
            <p>For California residents: We do not sell your personal information as defined by the CCPA.</p>
        </div>

        <div class="section">
            <h2>International Data Transfers</h2>
            <p>Your data may be processed in different countries where we maintain servers or our service providers are located. We ensure appropriate safeguards are in place through standard contractual clauses and data processing agreements.</p>
        </div>

        <div class="section">
            <h2>Your Privacy Rights</h2>
            <ul>
                <li>Access your personal data</li>
                <li>Request correction of inaccurate data</li>
                <li>Request deletion of your data (right to be forgotten)</li>
                <li>Export your data (data portability)</li>
                <li>Restrict or object to processing</li>
                <li>Withdraw consent at any time</li>
            </ul>
            <p>We do not sell your personal information to any third parties.</p>
        </div>

        <div class="section">
            <h2>Children's Privacy</h2>
            <p>Our services are not intended for children under 13. We do not knowingly collect information from children under 13. If we discover we have collected such information, we will promptly delete it.</p>
            <p>For teens aged 13-17, we require parental consent for account creation and data collection.</p>
        </div>

        <div class="section">
            <h2>Privacy Policy Updates</h2>
            <p>We may update this privacy policy to reflect changes in our practices or legal requirements. We will notify you of significant changes through the extension or email. Continued use of our services after changes constitutes acceptance of the updated policy.</p>
        </div>

        <div class="section">
            <h2>Contact Information</h2>
            <p>For privacy-related inquiries:</p>
            <ul>
                <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
            </ul>
        </div>
    </div>
</body>
</html>
