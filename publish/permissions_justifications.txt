Storage:
The extension uses the 'storage' permission to save user preferences and whitelist settings locally, ensuring that the filtering rules are applied consistently across browsing sessions.

Alarms:
The 'alarms' permission is used to schedule periodic checks for updates to the filtering rules and to verify subscription status, ensuring the extension remains functional and up-to-date.

tabs:
The 'tabs' permission is used to manage tabs when the extension needs to display warning pages, subscription pages, or other important information related to content blocking and parental controls.

history:
The 'history' permission is used to check recently visited pages to ensure proper application of parental controls and content filtering rules across browsing sessions.

Host Permissions ("<all_urls>"):
The extension requires broad host permissions to actively monitor and filter content across all websites. This is essential for providing comprehensive parental controls and cannot be limited to specific domains as children may access any website.

Specific Domain Permissions:
- https://smartparent.qubitrhythm.com/*: Required to connect to our AI-powered content analysis service
- http://localhost:8080/*: Used during development and testing only

Remote Code Usage:
The extension connects to smartparent.qubitrhythm.com to utilize our AI services for content filtering and analysis. This server hosts the AI models and analysis tools that power the extension's smart filtering capabilities. The server code is kept separate from the extension for security and to allow for real-time updates to our AI models without requiring extension updates. All communication with the server is done securely over HTTPS.
